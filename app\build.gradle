plugins {
    id 'com.android.application'
}

android {
    namespace 'com.bezruk.qrcodebarcode'
    compileSdk 34

    defaultConfig {
        applicationId "com.bezruk.qrcodebarcode"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 8
        versionName "1.0.7"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility = 1.8
        targetCompatibility = 1.8
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.browser:browser:1.7.0'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'com.google.android.material:material:1.10.0'
    implementation 'com.google.android.gms:play-services-ads:22.6.0'

    implementation 'com.google.zxing:core:3.3.3'

    implementation 'com.journeyapps:zxing-android-embedded:4.3.0'

    implementation 'com.github.clans:fab:1.6.4'

    implementation 'com.anjlab.android.iab.v3:library:1.0.44'
}