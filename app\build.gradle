plugins {
    id 'com.android.application'
}

android {
    namespace 'com.bezruk.qrcodebarcode'
    compileSdk 34

    defaultConfig {
        applicationId "com.bezruk.qrcodebarcode"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 8
        versionName "1.0.7"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        vectorDrawables.useSupportLibrary = true
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            minifyEnabled false
            debuggable true
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    buildFeatures {
        viewBinding true
        buildConfig true
    }

    packagingOptions {
        resources {
            excludes += ['META-INF/DEPENDENCIES', 'META-INF/LICENSE', 'META-INF/LICENSE.txt', 'META-INF/NOTICE', 'META-INF/NOTICE.txt']
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    // AndroidX Core Libraries
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.core:core:1.12.0'
    implementation 'androidx.core:core-splashscreen:1.0.1'
    implementation 'androidx.browser:browser:1.8.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.viewpager:viewpager:1.0.0'

    // Material Design
    implementation 'com.google.android.material:material:1.11.0'

    // Google Play Services
    implementation 'com.google.android.gms:play-services-ads:22.6.0'

    // ZXing for QR/Barcode scanning - keeping version for API 21+ compatibility
    implementation 'com.google.zxing:core:3.5.2'
    implementation 'com.journeyapps:zxing-android-embedded:4.3.0'

    // Color Picker
    implementation 'com.pes.materialcolorpicker:library:1.2.5'

    // Floating Action Button
    implementation 'com.github.clans:fab:1.6.4'

    // In-App Billing
    implementation 'com.anjlab.android.iab.v3:library:1.0.44'

    // Permission handling
    implementation 'com.karumi:dexter:6.2.3'

    // Image loading and caching
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.16.0'

    // Testing
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}