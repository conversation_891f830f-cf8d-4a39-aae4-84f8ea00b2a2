apply plugin: 'com.android.application'

android {
    compileSdkVersion 33
    defaultConfig {
        applicationId "com.bezruk.qrcodebarcode"
        minSdkVersion 21
        targetSdkVersion 33
        versionCode 7
        versionName "1.0.6"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility = 1.8
        targetCompatibility = 1.8
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.browser:browser:1.7.0'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0' // No newer version, consider removing if not needed
    implementation 'com.google.android.material:material:1.10.0'
    implementation 'com.google.android.gms:play-services-ads:22.6.0'

    implementation 'com.google.zxing:core:3.3.3' //Do not change the version for supporting devices with API below 21

    implementation 'com.pes.materialcolorpicker:library:1.2.5'

    implementation 'com.journeyapps:zxing-android-embedded:4.3.0'

    implementation 'com.github.clans:fab:1.6.4'

    implementation 'com.anjlab.android.iab.v3:library:1.0.44'
}