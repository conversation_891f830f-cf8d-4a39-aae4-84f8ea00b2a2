package com.bezruk.qrcodebarcode.utility;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.util.Log;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * Analytics helper for tracking app usage and performance
 */
public class AnalyticsHelper {
    
    private static final String TAG = "AnalyticsHelper";
    private static final String PREFS_NAME = "analytics_prefs";
    
    // Event types
    public static final String EVENT_SCAN_SUCCESS = "scan_success";
    public static final String EVENT_SCAN_FAILURE = "scan_failure";
    public static final String EVENT_QR_GENERATE = "qr_generate";
    public static final String EVENT_BARCODE_GENERATE = "barcode_generate";
    public static final String EVENT_SHARE_CODE = "share_code";
    public static final String EVENT_SAVE_CODE = "save_code";
    public static final String EVENT_GALLERY_IMPORT = "gallery_import";
    public static final String EVENT_FLASH_TOGGLE = "flash_toggle";
    public static final String EVENT_CAMERA_SWITCH = "camera_switch";
    public static final String EVENT_SETTINGS_OPEN = "settings_open";
    public static final String EVENT_HISTORY_VIEW = "history_view";
    public static final String EVENT_AD_SHOWN = "ad_shown";
    public static final String EVENT_AD_CLICKED = "ad_clicked";
    public static final String EVENT_APP_LAUNCH = "app_launch";
    public static final String EVENT_APP_BACKGROUND = "app_background";
    public static final String EVENT_PERMISSION_GRANTED = "permission_granted";
    public static final String EVENT_PERMISSION_DENIED = "permission_denied";
    
    // Parameters
    public static final String PARAM_CODE_TYPE = "code_type";
    public static final String PARAM_SCAN_TIME = "scan_time_ms";
    public static final String PARAM_ERROR_TYPE = "error_type";
    public static final String PARAM_PERMISSION_TYPE = "permission_type";
    public static final String PARAM_AD_TYPE = "ad_type";
    public static final String PARAM_CONTENT_LENGTH = "content_length";
    public static final String PARAM_SOURCE = "source";
    
    // Statistics keys
    private static final String KEY_TOTAL_SCANS = "total_scans";
    private static final String KEY_SUCCESSFUL_SCANS = "successful_scans";
    private static final String KEY_FAILED_SCANS = "failed_scans";
    private static final String KEY_QR_GENERATED = "qr_generated";
    private static final String KEY_BARCODE_GENERATED = "barcode_generated";
    private static final String KEY_CODES_SHARED = "codes_shared";
    private static final String KEY_CODES_SAVED = "codes_saved";
    private static final String KEY_APP_LAUNCHES = "app_launches";
    private static final String KEY_FIRST_LAUNCH_DATE = "first_launch_date";
    private static final String KEY_LAST_LAUNCH_DATE = "last_launch_date";
    private static final String KEY_TOTAL_SESSION_TIME = "total_session_time";
    private static final String KEY_SESSION_START_TIME = "session_start_time";
    
    private static AnalyticsHelper instance;
    private SharedPreferences prefs;
    private Context context;
    private long sessionStartTime;
    
    private AnalyticsHelper(Context context) {
        this.context = context.getApplicationContext();
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.sessionStartTime = System.currentTimeMillis();
    }
    
    public static synchronized AnalyticsHelper getInstance(Context context) {
        if (instance == null) {
            instance = new AnalyticsHelper(context);
        }
        return instance;
    }
    
    /**
     * Track an event with parameters
     */
    public void trackEvent(String eventName, Map<String, Object> parameters) {
        try {
            Log.d(TAG, "Event: " + eventName + ", Parameters: " + parameters);
            
            // Update local statistics
            updateLocalStats(eventName, parameters);
            
            // Here you can integrate with analytics services like Firebase Analytics
            // FirebaseAnalytics.getInstance(context).logEvent(eventName, bundleFromMap(parameters));
            
        } catch (Exception e) {
            Log.e(TAG, "Error tracking event: " + eventName, e);
        }
    }
    
    /**
     * Track an event without parameters
     */
    public void trackEvent(String eventName) {
        trackEvent(eventName, new HashMap<>());
    }
    
    /**
     * Track scan success
     */
    public void trackScanSuccess(String codeType, long scanTimeMs, int contentLength) {
        Map<String, Object> params = new HashMap<>();
        params.put(PARAM_CODE_TYPE, codeType);
        params.put(PARAM_SCAN_TIME, scanTimeMs);
        params.put(PARAM_CONTENT_LENGTH, contentLength);
        trackEvent(EVENT_SCAN_SUCCESS, params);
    }
    
    /**
     * Track scan failure
     */
    public void trackScanFailure(String errorType) {
        Map<String, Object> params = new HashMap<>();
        params.put(PARAM_ERROR_TYPE, errorType);
        trackEvent(EVENT_SCAN_FAILURE, params);
    }
    
    /**
     * Track code generation
     */
    public void trackCodeGeneration(String codeType, int contentLength) {
        Map<String, Object> params = new HashMap<>();
        params.put(PARAM_CODE_TYPE, codeType);
        params.put(PARAM_CONTENT_LENGTH, contentLength);
        
        if ("QR_CODE".equals(codeType)) {
            trackEvent(EVENT_QR_GENERATE, params);
        } else {
            trackEvent(EVENT_BARCODE_GENERATE, params);
        }
    }
    
    /**
     * Track permission result
     */
    public void trackPermissionResult(String permissionType, boolean granted) {
        Map<String, Object> params = new HashMap<>();
        params.put(PARAM_PERMISSION_TYPE, permissionType);
        
        if (granted) {
            trackEvent(EVENT_PERMISSION_GRANTED, params);
        } else {
            trackEvent(EVENT_PERMISSION_DENIED, params);
        }
    }
    
    /**
     * Track ad events
     */
    public void trackAdEvent(String eventType, String adType) {
        Map<String, Object> params = new HashMap<>();
        params.put(PARAM_AD_TYPE, adType);
        trackEvent(eventType, params);
    }
    
    /**
     * Start session tracking
     */
    public void startSession() {
        sessionStartTime = System.currentTimeMillis();
        prefs.edit().putLong(KEY_SESSION_START_TIME, sessionStartTime).apply();
        
        // Track app launch
        incrementCounter(KEY_APP_LAUNCHES);
        updateLastLaunchDate();
        setFirstLaunchDateIfNeeded();
        
        trackEvent(EVENT_APP_LAUNCH);
    }
    
    /**
     * End session tracking
     */
    public void endSession() {
        long sessionDuration = System.currentTimeMillis() - sessionStartTime;
        long totalSessionTime = prefs.getLong(KEY_TOTAL_SESSION_TIME, 0);
        prefs.edit().putLong(KEY_TOTAL_SESSION_TIME, totalSessionTime + sessionDuration).apply();
        
        trackEvent(EVENT_APP_BACKGROUND);
    }
    
    /**
     * Get usage statistics
     */
    public UsageStats getUsageStats() {
        return new UsageStats(
            prefs.getInt(KEY_TOTAL_SCANS, 0),
            prefs.getInt(KEY_SUCCESSFUL_SCANS, 0),
            prefs.getInt(KEY_FAILED_SCANS, 0),
            prefs.getInt(KEY_QR_GENERATED, 0),
            prefs.getInt(KEY_BARCODE_GENERATED, 0),
            prefs.getInt(KEY_CODES_SHARED, 0),
            prefs.getInt(KEY_CODES_SAVED, 0),
            prefs.getInt(KEY_APP_LAUNCHES, 0),
            prefs.getLong(KEY_TOTAL_SESSION_TIME, 0),
            prefs.getString(KEY_FIRST_LAUNCH_DATE, ""),
            prefs.getString(KEY_LAST_LAUNCH_DATE, "")
        );
    }
    
    /**
     * Reset all statistics
     */
    public void resetStats() {
        prefs.edit().clear().apply();
    }
    
    private void updateLocalStats(String eventName, Map<String, Object> parameters) {
        switch (eventName) {
            case EVENT_SCAN_SUCCESS:
                incrementCounter(KEY_SUCCESSFUL_SCANS);
                incrementCounter(KEY_TOTAL_SCANS);
                break;
            case EVENT_SCAN_FAILURE:
                incrementCounter(KEY_FAILED_SCANS);
                incrementCounter(KEY_TOTAL_SCANS);
                break;
            case EVENT_QR_GENERATE:
                incrementCounter(KEY_QR_GENERATED);
                break;
            case EVENT_BARCODE_GENERATE:
                incrementCounter(KEY_BARCODE_GENERATED);
                break;
            case EVENT_SHARE_CODE:
                incrementCounter(KEY_CODES_SHARED);
                break;
            case EVENT_SAVE_CODE:
                incrementCounter(KEY_CODES_SAVED);
                break;
        }
    }
    
    private void incrementCounter(String key) {
        int currentValue = prefs.getInt(key, 0);
        prefs.edit().putInt(key, currentValue + 1).apply();
    }
    
    private void updateLastLaunchDate() {
        String currentDate = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            .format(new Date());
        prefs.edit().putString(KEY_LAST_LAUNCH_DATE, currentDate).apply();
    }
    
    private void setFirstLaunchDateIfNeeded() {
        if (!prefs.contains(KEY_FIRST_LAUNCH_DATE)) {
            String currentDate = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                .format(new Date());
            prefs.edit().putString(KEY_FIRST_LAUNCH_DATE, currentDate).apply();
        }
    }
    
    /**
     * Usage statistics data class
     */
    public static class UsageStats {
        public final int totalScans;
        public final int successfulScans;
        public final int failedScans;
        public final int qrGenerated;
        public final int barcodeGenerated;
        public final int codesShared;
        public final int codesSaved;
        public final int appLaunches;
        public final long totalSessionTimeMs;
        public final String firstLaunchDate;
        public final String lastLaunchDate;
        
        public UsageStats(int totalScans, int successfulScans, int failedScans,
                         int qrGenerated, int barcodeGenerated, int codesShared,
                         int codesSaved, int appLaunches, long totalSessionTimeMs,
                         String firstLaunchDate, String lastLaunchDate) {
            this.totalScans = totalScans;
            this.successfulScans = successfulScans;
            this.failedScans = failedScans;
            this.qrGenerated = qrGenerated;
            this.barcodeGenerated = barcodeGenerated;
            this.codesShared = codesShared;
            this.codesSaved = codesSaved;
            this.appLaunches = appLaunches;
            this.totalSessionTimeMs = totalSessionTimeMs;
            this.firstLaunchDate = firstLaunchDate;
            this.lastLaunchDate = lastLaunchDate;
        }
        
        public double getSuccessRate() {
            return totalScans > 0 ? (double) successfulScans / totalScans * 100 : 0;
        }
        
        public long getAverageSessionTimeMs() {
            return appLaunches > 0 ? totalSessionTimeMs / appLaunches : 0;
        }
    }
}
