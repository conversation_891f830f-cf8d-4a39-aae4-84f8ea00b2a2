<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Scanner MAX - Web Version</title>
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#2196F3">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem;
            text-align: center;
            color: white;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .header p {
            opacity: 0.9;
        }
        
        .container {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            gap: 2rem;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 500px;
            width: 100%;
            text-align: center;
        }
        
        .scanner-area {
            width: 100%;
            height: 300px;
            border: 3px dashed #2196F3;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 1rem 0;
            background: #f8f9fa;
            position: relative;
            overflow: hidden;
        }
        
        #video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 12px;
        }
        
        .scanner-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 200px;
            border: 3px solid #2196F3;
            border-radius: 15px;
            background: transparent;
            pointer-events: none;
        }
        
        .scanner-overlay::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            border: 3px solid transparent;
            border-top-color: #ff4444;
            border-radius: 15px;
            animation: scan 2s linear infinite;
        }
        
        @keyframes scan {
            0% { transform: translateY(-100%); }
            100% { transform: translateY(300%); }
        }
        
        .buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 1rem;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(45deg, #2196F3, #21CBF3);
            color: white;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .btn.secondary {
            background: linear-gradient(45deg, #666, #888);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .result {
            margin-top: 1rem;
            padding: 1rem;
            background: #e8f5e8;
            border-radius: 10px;
            border-left: 4px solid #4caf50;
            display: none;
        }
        
        .result.show {
            display: block;
            animation: slideIn 0.3s ease;
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.9);
            padding: 1.5rem;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            background: #4caf50;
            color: white;
            border-radius: 25px;
            display: none;
            z-index: 1000;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .card {
                padding: 1.5rem;
            }
            
            .buttons {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="status" id="status">جاهز للمسح</div>
    
    <div class="header">
        <h1>🔍 QR Scanner MAX</h1>
        <p>نسخة الويب - مسح وإنشاء رموز QR بسهولة</p>
    </div>
    
    <div class="container">
        <div class="card">
            <h2>📱 مسح رمز QR</h2>
            <div class="scanner-area">
                <video id="video" autoplay muted playsinline></video>
                <div class="scanner-overlay"></div>
            </div>
            
            <div class="buttons">
                <button class="btn" onclick="startScanner()">🎥 بدء المسح</button>
                <button class="btn secondary" onclick="stopScanner()">⏹️ إيقاف</button>
                <button class="btn" onclick="switchCamera()">🔄 تبديل الكاميرا</button>
            </div>
            
            <div class="result" id="result">
                <h3>✅ نتيجة المسح:</h3>
                <p id="resultText"></p>
                <div class="buttons" style="margin-top: 1rem;">
                    <button class="btn" onclick="copyResult()">📋 نسخ</button>
                    <button class="btn" onclick="shareResult()">📤 مشاركة</button>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h2>🎨 إنشاء رمز QR</h2>
            <input type="text" id="textInput" placeholder="أدخل النص هنا..." 
                   style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 10px; margin: 1rem 0; font-size: 1rem;">
            
            <div class="buttons">
                <button class="btn" onclick="generateQR()">✨ إنشاء QR</button>
                <button class="btn secondary" onclick="downloadQR()">💾 تحميل</button>
            </div>
            
            <canvas id="qrCanvas" style="max-width: 100%; margin-top: 1rem; display: none; border-radius: 10px;"></canvas>
        </div>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">📱</div>
                <h3>مسح سريع</h3>
                <p>مسح رموز QR بسرعة عالية</p>
            </div>
            <div class="feature">
                <div class="feature-icon">🎨</div>
                <h3>إنشاء مخصص</h3>
                <p>إنشاء رموز QR مخصصة</p>
            </div>
            <div class="feature">
                <div class="feature-icon">💾</div>
                <h3>حفظ ومشاركة</h3>
                <p>حفظ ومشاركة النتائج</p>
            </div>
            <div class="feature">
                <div class="feature-icon">🌐</div>
                <h3>يعمل في المتصفح</h3>
                <p>لا يحتاج تثبيت</p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script src="app.js"></script>
</body>
</html>
