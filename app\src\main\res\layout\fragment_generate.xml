<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/TopLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30px"
        android:layout_marginTop="10px"
        android:layout_marginRight="30px"
        android:gravity="center"
        android:orientation="horizontal">

        <HorizontalScrollView
            android:id="@+id/TopScrollBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <LinearLayout
                android:id="@+id/BtnLayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_horizontal|center"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/text_btn"
                    android:layout_width="45dp"
                    android:layout_height="45dp"
                    android:layout_marginRight="2dp"
                    android:layout_weight="1"
                    android:background="@color/colorPrimary"
                    android:padding="7dp"
                    app:srcCompat="@drawable/ic_plain_text_white" />

                <ImageView
                    android:id="@+id/contact_btn"
                    android:layout_width="45dp"
                    android:layout_height="45dp"
                    android:layout_marginLeft="2dp"
                    android:layout_marginRight="2dp"
                    android:layout_weight="1"
                    android:background="@color/grey"
                    android:padding="7dp"
                    app:srcCompat="@drawable/ic_contact_white" />

                <ImageView
                    android:id="@+id/url_btn"
                    android:layout_width="45dp"
                    android:layout_height="45dp"
                    android:layout_marginLeft="2dp"
                    android:layout_marginRight="2dp"
                    android:layout_weight="1"
                    android:background="@color/grey"
                    android:padding="5dp"
                    app:srcCompat="@drawable/ic_web_white" />

                <ImageView
                    android:id="@+id/wifi_btn"
                    android:layout_width="45dp"
                    android:layout_height="45dp"
                    android:layout_marginLeft="2dp"
                    android:layout_marginRight="2dp"
                    android:layout_weight="1"
                    android:background="@color/grey"
                    android:padding="5dp"
                    app:srcCompat="@drawable/ic_wifi_white" />

                <ImageView
                    android:id="@+id/email_btn"
                    android:layout_width="45dp"
                    android:layout_height="45dp"
                    android:layout_marginLeft="2dp"
                    android:layout_marginRight="2dp"
                    android:layout_weight="1"
                    android:background="@color/grey"
                    android:padding="6dp"
                    app:srcCompat="@drawable/ic_email_white" />

                <ImageView
                    android:id="@+id/barcode_btn"
                    android:layout_width="45dp"
                    android:layout_height="45dp"
                    android:layout_marginLeft="2dp"
                    android:layout_marginRight="2dp"
                    android:layout_weight="1"
                    android:background="@color/grey"
                    android:padding="6dp"
                    app:srcCompat="@drawable/ic_barcode_white" />

                <ImageView
                    android:id="@+id/sms_btn"
                    android:layout_width="45dp"
                    android:layout_height="45dp"
                    android:layout_marginLeft="2dp"
                    android:layout_weight="1"
                    android:background="@color/grey"
                    android:padding="6dp"
                    app:srcCompat="@drawable/ic_sms_white" />

            </LinearLayout>
        </HorizontalScrollView>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/TopLayout"
        android:layout_alignParentStart="true"
        android:layout_alignParentBottom="true"
        android:orientation="horizontal">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <RelativeLayout
                    android:id="@+id/inputLayout"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginLeft="30px"
                    android:layout_marginTop="20px"
                    android:layout_marginRight="30px"
                    android:layout_marginBottom="10px"
                    android:background="@drawable/custom_input">

                    <EditText
                        android:id="@+id/inputText"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginBottom="0dp"
                        android:background="@android:color/transparent"
                        android:hint="@string/type_here_text"
                        android:imeOptions="actionDone"
                        android:inputType="none|text"
                        android:lines="1"
                        android:maxLines="1"
                        android:paddingLeft="20dp"
                        android:paddingRight="50dp" />

                    <ImageButton
                        android:id="@+id/contact_choose_btn"
                        android:layout_width="26dp"
                        android:layout_height="26dp"
                        android:layout_alignParentTop="true"
                        android:layout_alignParentEnd="true"
                        android:layout_marginTop="12dp"
                        android:layout_marginRight="13dp"
                        android:background="#00ffffff"
                        android:scaleType="fitXY"
                        app:srcCompat="@drawable/ic_contact_plus_btn" />

                </RelativeLayout>


                <RelativeLayout
                    android:id="@+id/inputLayout2"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_below="@+id/inputLayout"
                    android:layout_centerHorizontal="true"
                    android:layout_marginLeft="30px"
                    android:layout_marginRight="30px"
                    android:background="@drawable/custom_input">

                    <EditText
                        android:id="@+id/inputText2"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@android:color/transparent"
                        android:gravity="top"
                        android:hint="@string/type_here_text"
                        android:imeOptions="actionDone"
                        android:inputType="text|textMultiLine|textImeMultiLine"
                        android:paddingLeft="20dp"
                        android:paddingTop="10dp"
                        android:paddingRight="50dp"
                        android:paddingBottom="10dp" />

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/inputLayout3"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_below="@+id/inputLayout2"
                    android:layout_centerHorizontal="true"
                    android:layout_marginLeft="30px"
                    android:layout_marginRight="30px"
                    android:background="@drawable/custom_input">

                    <EditText
                        android:id="@+id/inputText3"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@android:color/transparent"
                        android:gravity="top"
                        android:hint="@string/type_here_text"
                        android:imeOptions="actionDone"
                        android:inputType="text|textMultiLine|textImeMultiLine"
                        android:paddingLeft="20dp"
                        android:paddingTop="10dp"
                        android:paddingRight="50dp"
                        android:paddingBottom="10dp" />


                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/inputLayout4"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_below="@+id/inputLayout3"
                    android:layout_centerHorizontal="true"
                    android:layout_marginLeft="30px"
                    android:layout_marginRight="30px"
                    android:background="@drawable/custom_input">

                    <EditText
                        android:id="@+id/inputText4"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@android:color/transparent"
                        android:gravity="top"
                        android:hint="@string/type_here_text"
                        android:imeOptions="actionDone"
                        android:inputType="text|textMultiLine|textImeMultiLine"
                        android:paddingLeft="20dp"
                        android:paddingTop="10dp"
                        android:paddingRight="50dp"
                        android:paddingBottom="10dp" />

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/inputLayout5"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_below="@+id/inputLayout4"
                    android:layout_centerHorizontal="true"
                    android:layout_marginLeft="30px"
                    android:layout_marginRight="30px"
                    android:background="@drawable/custom_input">

                    <Spinner
                        android:id="@+id/wifi_type"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:entries="@array/wifi_security_list" />


                </RelativeLayout>

                <TextView
                    android:id="@+id/generate_btn"
                    style="@style/Widget.AppCompat.Button.Colored"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/inputLayout5"
                    android:layout_centerHorizontal="true"
                    android:layout_marginLeft="30px"
                    android:layout_marginTop="10px"
                    android:layout_marginRight="30px"
                    android:background="@color/grey"
                    android:text="CREATE"
                    android:textColorLink="@color/colorAccent"
                    android:textSize="24sp" />

                <ImageView
                    android:id="@+id/outputBitmap"
                    android:layout_width="match_parent"
                    android:layout_height="320dp"
                    android:layout_below="@+id/generate_btn"
                    android:layout_marginTop="5dp"
                    android:padding="10dp"
                    android:scaleType="fitCenter"
                    android:src="@drawable/qr_bg1" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/outputBitmap"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="5dp"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/color"
                        android:layout_width="45dp"
                        android:layout_height="45dp"
                        android:layout_marginLeft="10dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginRight="10dp"
                        android:layout_marginBottom="14dp"
                        android:background="@color/colorPrimary"
                        android:padding="7dp"
                        android:src="@drawable/ic_color"
                        app:elevation="3dp" />

                    <ImageView
                        android:id="@+id/save"
                        android:layout_width="45dp"
                        android:layout_height="45dp"
                        android:layout_marginLeft="10dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginRight="10dp"
                        android:layout_marginBottom="14dp"
                        android:background="@color/colorPrimary"
                        android:padding="7dp"
                        android:src="@drawable/ic_save"
                        app:elevation="3dp" />

                    <ImageView
                        android:id="@+id/share"
                        android:layout_width="45dp"
                        android:layout_height="45dp"
                        android:layout_marginLeft="10dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginRight="10dp"
                        android:layout_marginBottom="14dp"
                        android:background="@color/colorPrimary"
                        android:padding="7dp"
                        android:src="@drawable/ic_share"
                        app:elevation="3dp" />

                </LinearLayout>

            </RelativeLayout>

        </ScrollView>
    </LinearLayout>

</RelativeLayout>
