<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="activity.MainActivity">


    <com.google.android.gms.ads.AdView
        android:id="@+id/adViewMain"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:visibility="gone"
        app:adSize="BANNER"
        app:adUnitId="@string/banner_ad_unit_id" />

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/viewpager"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/navigation"
        android:layout_below="@+id/adViewMain">

    </androidx.viewpager.widget.ViewPager>

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_alignParentBottom="true"
        android:background="@color/colorPrimary"
        android:persistentDrawingCache="none|animation"
        app:itemBackground="@color/colorPrimary"
        app:itemIconTint="@drawable/bottom_navigation_item_background_colors"
        app:itemTextColor="@drawable/bottom_navigation_item_background_colors"
        app:menu="@menu/bottom_navigation">

    </com.google.android.material.bottomnavigation.BottomNavigationView>

</RelativeLayout>
