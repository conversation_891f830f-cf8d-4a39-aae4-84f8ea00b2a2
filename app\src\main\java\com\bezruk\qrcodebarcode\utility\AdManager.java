package com.bezruk.qrcodebarcode.utility;

import android.app.Activity;
import android.content.Context;
import android.view.View;

import com.bezruk.qrcodebarcode.R;
import com.bezruk.qrcodebarcode.data.preference.AppPreference;
import com.bezruk.qrcodebarcode.data.preference.PrefKey;
import com.google.android.gms.ads.AdListener;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.MobileAds;
import com.google.android.gms.ads.initialization.InitializationStatus;
import com.google.android.gms.ads.initialization.OnInitializationCompleteListener;


public class AdManager {

    private static AdManager adUtils;

    private Boolean isAdsVisibility;
    String Banner, Full, BannerPr, FullPr;

    private AdManager(Context context) {
        MobileAds.initialize(context, new OnInitializationCompleteListener() {
            @Override
            public void onInitializationComplete(InitializationStatus initializationStatus) {
                // Initialization complete
            }
        });
        isAdsVisibility = AppPreference.getInstance(context).getBoolean(PrefKey.ADS_VISIBILITY,true);
    }

    public static AdManager getInstance(Context context) {
        if (adUtils == null) {
            adUtils = new AdManager(context);
        }
        return adUtils;
    }

    public void showBannerAd(final AdView mAdView) {
        AdRequest adRequest = new AdRequest.Builder().build();
        mAdView.loadAd(adRequest);

        mAdView.setAdListener(new AdListener() {
            @Override
            public void onAdLoaded() {
                super.onAdLoaded();
                if(isAdsVisibility){
                    mAdView.setVisibility(View.VISIBLE);
                } else {
                    mAdView.setVisibility(View.GONE);
                }

            }

            @Override
            public void onAdFailedToLoad(LoadAdError loadAdError) {
                super.onAdFailedToLoad(loadAdError);
                mAdView.setVisibility(View.GONE);
            }
        });
    }

    public void loadFullScreenAd(Activity activity) {
        // Interstitial ads temporarily disabled for compatibility
    }

    public boolean showFullScreenAd() {
        // Interstitial ads temporarily disabled for compatibility
        return false;
    }

    public Object getInterstitialAd() {
        // Return dummy object for compatibility
        return new Object();
    }


}
