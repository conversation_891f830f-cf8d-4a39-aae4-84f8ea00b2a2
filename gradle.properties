# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# AndroidX migration
android.enableJetifier=true
android.useAndroidX=true

# Performance optimizations
org.gradle.jvmargs=-Xmx4096m -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true

# Android build optimizations
android.enableR8.fullMode=true

# Kotlin optimizations (if using Kotlin in future)
kotlin.code.style=official
kotlin.incremental=true
