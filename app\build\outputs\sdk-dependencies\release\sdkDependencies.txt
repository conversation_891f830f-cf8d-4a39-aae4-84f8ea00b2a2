# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.6.1"
  }
  digests {
    sha256: "~\245W;\223\253\253\323\27521$Q\306\352H\246b\260:\024\r\332\201\256\276uwj \244\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.8.0"
  }
  digests {
    sha256: "\323\246vp\235\352\004\362\250Pn*\350PR\377\367c\333Rj\307\361k\004\336P\375\320[\a "
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.6.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.6.0"
  }
  digests {
    sha256: "`\261\v^\365v\233yW\001r\340\025\270\025\224\005\311/\003K\250\213\223\221\251wX\234\235\353N"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.8.22"
  }
  digests {
    sha256: "\003\245\303\226\\\303pQ\022\216d\344gH\343\224\266\275L\227\372\201\306\336o\307+\375D\343B\033"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.8.22"
  }
  digests {
    sha256: "\320\3026^$7\357p\363E\206\325\017\005WC\367\227\026\274\376e\344\274r9\315\322f\236\367\305"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.1.0"
  }
  digests {
    sha256: "c*\016T\aF\035\347t@\223R\224\016)*)\0207rB\a\247\207\202\fw\332\367\323;r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.9.0"
  }
  digests {
    sha256: "\213\332>\343\250\210\207\325Ofy\373kl\327\210b\237s#J\311\034\213\276\331$\347!\354\205\270"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.3.0"
  }
  digests {
    sha256: "\253\375)\310Un[\3202Z\237v\232\271\351\321T\377JU\025\304v\315\325\242\250([\033\031\334"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.6.1"
  }
  digests {
    sha256: "Hg\375Ryt/\272\203\210\202\0310\313*\377\340m\201\245(\024\347\344\036p9.\240\357\210|"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.6.1"
  }
  digests {
    sha256: "\363H1\266\307\034\330D\341\323]\033\344\235^yD|Z\270V4e1\261\350go\332st\261"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.1"
  }
  digests {
    sha256: "\020s\023v\f\030\370\332\027N\215\201\003PJF\216\200n\210\367\265Z\204\275\034\016\256\352\021\216\232"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.1"
  }
  digests {
    sha256: "t\226\317\375\323\353\020\020\232\315\332\0342\022\366\254x\025x\236\t8\r\311\342\314\336\304\226\333\243\374"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.22"
  }
  digests {
    sha256: "A\230\260\352\360\220\244\362[o~ZYX\037C\024\272\214\237l\321\321>\351\323H\346^\330\367\a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.8.22"
  }
  digests {
    sha256: "\005_\\\262B\207\372\020a\000\231Z{G\253\222\022k\201\3502\350u\365\372,\360\275Ui=\v"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.6.1"
  }
  digests {
    sha256: "g5\237`\235\374+\366]\241\'\v#\003?\205`d\354\'\237\005\216\np\307\025\367\311\00001"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.6.1"
  }
  digests {
    sha256: "\"Vx\n<\377J\036W\373\263\324BU|\027\3346:\270\257\020[\312\365&\035\216-]\271I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.6.1"
  }
  digests {
    sha256: "\0173\261\275\001\177\226Zj\373.{\363\343\2754<b@a\301\230m\352\213\242F\235\0044\2247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-service"
    version: "2.6.1"
  }
  digests {
    sha256: "\212\316\2311?\n\346\257G\031K\312\376(\363D\343c\364\322\223\370K+\227\264\201s[\004\336\322"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.6.1"
  }
  digests {
    sha256: "\344\377C8\231\236\034l\234rG\031\365\324\252}\326\033\366\365E\325%j\'\251\323u\337\237#0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.6.1"
  }
  digests {
    sha256: "\310/\211\"\032\333\341\235\367\307\255\272\266?N\314\205\177\307F\343\311IBV\253\217\245\302\004\221\262"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.9.0"
  }
  digests {
    sha256: "\025B\241\337{\351\b\311_\356\221\270\333\300?\331t\365?\021\330J\205\330\201\371ZRU\034@Q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.0"
  }
  digests {
    sha256: "4\350\262\277\307N#\301R^=\251\003\256D\233\177\033D\n\357E\341\201Y\356G\016\221\231\177H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.6.1"
  }
  digests {
    sha256: "\333\221]\277I5xc\336\026i\377\237\335\216\220\b\326_\343W\257l\316\232\3460C\255_f\027"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.2.0"
  }
  digests {
    sha256: "\363\032\006\301P\354\2600s\365Zo{\vt\242@\246\250\327\'\301L\347g&\320 W\r\372\214"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.2.0"
  }
  digests {
    sha256: "\177\372MFM\235\262Y\374\240\315\265\017\275J\266=hr\274\332YF\213\237uUPL}Z\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.3.6"
  }
  digests {
    sha256: "\022\360\203\033O\b\t-]\332\',\031#\301\032\002/\362\f\357\376\323\350\001u\036!\273\215\034\036"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.browser"
    artifactId: "browser"
    version: "1.7.0"
  }
  digests {
    sha256: "\340;n\370\306\327\241H\210g\002\253\315)\225\356\035K\300I,\254_\306\252I\304r\374\340k\223"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-v4"
    version: "1.0.0"
  }
  digests {
    sha256: "x\376\301H_\0178\212GI\002-\325\024\026\205q\'\315%D\256\034?\320\261e\211\005T\200\260"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media"
    artifactId: "media"
    version: "1.0.0"
  }
  digests {
    sha256: "\262;R{+\254\207\fJtQ\346\230-q2\344\023\350\215\177\'\333\353\037\307d\nr\f\331\356"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-ui"
    version: "1.0.0"
  }
  digests {
    sha256: "\r\022`\306\347\346\2437\370u\337q\265\026\223\036p?qn\220\210\230\027\315: \372Z\303\331G"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.slidingpanelayout"
    artifactId: "slidingpanelayout"
    version: "1.0.0"
  }
  digests {
    sha256: "v\277\373|\357\277x\a\224\330\201p\002\332\321V/>\'\300\251\367F\326$\001\310\355\263\n\356\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.swiperefreshlayout"
    artifactId: "swiperefreshlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\227a\263\250\t\311\260\223\375\006\243\304\273\306Eum\354\016\225\265\311\332A\233\311\362\243\363\002n\215"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.asynclayoutinflater"
    artifactId: "asynclayoutinflater"
    version: "1.0.0"
  }
  digests {
    sha256: "\367\352\266\fW\255\335\224\273\006\'X2\376v\000a\033\352\252\341\241\354Y|#\031V\372\371l\213"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.10.0"
  }
  digests {
    sha256: "- N\344\361\026\271\210\246\246\265\333\214\364=\343\370\247M\n\341\272\027Xo\220\252\343\216>\032-"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-bom"
    version: "1.8.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.15.0"
  }
  digests {
    sha256: "\006pGqCI\347x\232[\333\372\331\321\300\257\237:\036\262\214U\240\356?h\346\202\371\005\304\353"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.0.1"
  }
  digests {
    sha256: "\354\025\265\324\242\357\360x\210\274\024\231\316.,n\376$\300\355`\314W\260\214\235\304\266\375<!\211"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-solver"
    version: "2.0.1"
  }
  digests {
    sha256: "\26272\355\2735\021\3317\376\241\377\357\004{\016l\000\033P\301\222\037\r\225\237\303\204\327\006\354j"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.1.0"
  }
  digests {
    sha256: "\360\322\265\246}\n\221\356\033\034s\357+cj\201\363V9%\335\321Z\035N\034A\354(\336zO"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.2.0"
  }
  digests {
    sha256: "\241\340Y\263\274\vC\245\215\354\016\376\315\312\250\234\202\322\274\245R\352[\254\366elF\350S\025~"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.0.0"
  }
  digests {
    sha256: "\351\\\0001\324\314$|\324\201\226\306(~X\322\316\345M\234y\270Z\376\247\311\t 3\002u\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads"
    version: "22.6.0"
  }
  digests {
    sha256: "\\Q\223\252V?\0236\313\251\016\032\237y|\250\275\237\225\214\277u%\364~i\b5\335f\3327"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices"
    version: "1.0.0-beta05"
  }
  digests {
    sha256: "\001\'W\214\334\355\255|\216\316\027H\024\306\364\343\002x\216\217{\321N\203=\221\234\033vY\331}"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices-java"
    version: "1.0.0-beta05"
  }
  digests {
    sha256: "\227\352S+F\274\203\365\254\344\a\342B\247\254\315\330+\204\2407u\331n\257\\\341`\316\275\273\234"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "31.1-android"
  }
  digests {
    sha256: "2\254.\327\t\331m\'\213].>\\\352\027\217\244\223\2319\305%\373du2\360\0230\215\263\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.1"
  }
  digests {
    sha256: "\241q\356LsM\322\332\203~K\026\276\235\364f\032\372\267*A\255\2571\353\204\337\332\3716\312&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.checkerframework"
    artifactId: "checker-qual"
    version: "3.12.0"
  }
  digests {
    sha256: "\377\020xZ\302\243W\354]\351\302\223\313\230*,\273`\\\003\t\352L\301\313\233\233\306\333\347\363\313"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.j2objc"
    artifactId: "j2objc-annotations"
    version: "1.3"
  }
  digests {
    sha256: "!\2570\311\"g\275a\"\300\340\264\322\f\314\266d\0327\352\371V\306T\016\304q\325\204\346J{"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-base"
    version: "22.6.0"
  }
  digests {
    sha256: "\"J\246\'\3464\316k\220\245\334=\322\326>\255,\216Iqm\233u\264\a\205KV\365\227\320\337"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.2.0"
  }
  digests {
    sha256: "\357C\353\374d\035qH\025CROF\321&y;L\265{\364f\304\337L\344=\f\265\341\033\221"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-identifier"
    version: "18.0.0"
  }
  digests {
    sha256: "s\f\233g\344\370]\2738I\364\363\344*PG\316\341\365\234#&F\235\001\331m\017\275!\030\032"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-lite"
    version: "22.6.0"
  }
  digests {
    sha256: "\376$\373W\301P7\324\231\375#O\373*\337\355Iq\212\257S\273\253\022&\363\003\241{b\371\352"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime"
    version: "2.7.0"
  }
  digests {
    sha256: "\210\202>\000\272\232\256m\306Cj\355Y(\300p\326\212b\302X\035\351ne\\\250o\003\016r\251"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.2.5"
  }
  digests {
    sha256: "$\245T\233ynC\3437Q=)\b\255\254g\364SP\331\251\v\312~.a i!@\273\024"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.2.5"
  }
  digests {
    sha256: "+\023\r\324\241\323\331\033g\001\3553\tm8\237\001\304\374\021\227\247\254\326\271\027$\335\305\254\374\006"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.1.0"
  }
  digests {
    sha256: "\206ss\177\333.\373\255\221\256\256\355\031\'\353\262\222\022\323j\206}\223\271c\234\200i\001\237\212\036"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.1.0"
  }
  digests {
    sha256: "\203A\377\t-``\326*\a\"\177)#qU\377\363o\261o\226\311_\275\232\210N7]\271\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk-api"
    version: "20.1.2"
  }
  digests {
    sha256: "\315\221\rE\276~\276;p\311\244\301l\233\356e\350t\201\025\355@\0226C\230O5\351\033_8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-base"
    version: "20.1.2"
  }
  digests {
    sha256: "\356\224}z\017\342,Z2\357l\233oz\020z\352j\320pDu\207N\326is\bC\034{\276"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.ump"
    artifactId: "user-messaging-platform"
    version: "2.1.0"
  }
  digests {
    sha256: "kZ\350\336\302g\364\016\365\262\213\323\375\255\231\361\221\321\225N`\000\352^x\316^\341\032Y\240o"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-appset"
    version: "16.0.1"
  }
  digests {
    sha256: "\340N\315Ou\317E\312,7\273S\373 \224Z\234Z2,\025\341l$\031pmg/\260\020\016"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.0.0"
  }
  digests {
    sha256: "?0\211PA\374\352F\334Z\301Q\243|\r\350\243\241i\265 \000zS\320\227\201\235\363\205\337`"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.0.1"
  }
  digests {
    sha256: "\361\006\333H\306\314\372\216\023\025\247\255\304J\354\320/\3675^\263\372}\315\313\250\302\203\250\353\026\201"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.zxing"
    artifactId: "core"
    version: "3.4.1"
  }
  digests {
    sha256: "\254\37203\214&&{l\253fU\t\302%\033\271n\203P\277\312\330\235Y\271I\272w\001\037\215"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.journeyapps"
    artifactId: "zxing-android-embedded"
    version: "4.3.0"
  }
  digests {
    sha256: "J\2603S\022|4\345\\\273\200\373\311\243@1\336\313\317\261\354^\217\231\031D\322\324\224b\0323"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.clans"
    artifactId: "fab"
    version: "1.6.4"
  }
  digests {
    sha256: "S\022\223\025\370\376\263\354\321\005xP\001\306%\021G[\220u\252\rE\366\"\276\327}S*\324H"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.anjlab.android.iab.v3"
    artifactId: "library"
    version: "1.0.44"
  }
  digests {
    sha256: "|\306\313UL\327\341K\036M\362Z\004\230\027\356Y`@!_\342\235y_\342 \016>S\324)"
  }
  repo_index {
    value: 1
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 34
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 30
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 12
  library_dep_index: 28
  library_dep_index: 46
  library_dep_index: 31
  library_dep_index: 4
  library_dep_index: 34
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 12
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 32
  library_dep_index: 31
  library_dep_index: 26
  library_dep_index: 4
}
library_dependencies {
  library_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 4
  library_dep_index: 5
  library_dep_index: 6
}
library_dependencies {
  library_index: 7
  library_dep_index: 2
}
library_dependencies {
  library_index: 8
  library_dep_index: 2
  library_dep_index: 9
  library_dep_index: 7
  library_dep_index: 10
  library_dep_index: 12
  library_dep_index: 33
  library_dep_index: 30
}
library_dependencies {
  library_index: 9
  library_dep_index: 4
}
library_dependencies {
  library_index: 10
  library_dep_index: 2
  library_dep_index: 11
}
library_dependencies {
  library_index: 12
  library_dep_index: 2
  library_dep_index: 13
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 32
  library_dep_index: 4
  library_dep_index: 15
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 27
}
library_dependencies {
  library_index: 13
  library_dep_index: 2
}
library_dependencies {
  library_index: 14
  library_dep_index: 2
  library_dep_index: 13
}
library_dependencies {
  library_index: 15
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 16
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 12
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
}
library_dependencies {
  library_index: 16
  library_dep_index: 17
  library_dep_index: 19
  library_dep_index: 20
}
library_dependencies {
  library_index: 17
  library_dep_index: 18
}
library_dependencies {
  library_index: 18
  library_dep_index: 6
  library_dep_index: 19
  library_dep_index: 5
  library_dep_index: 20
}
library_dependencies {
  library_index: 19
  library_dep_index: 16
  library_dep_index: 18
  library_dep_index: 17
}
library_dependencies {
  library_index: 20
  library_dep_index: 4
  library_dep_index: 21
}
library_dependencies {
  library_index: 21
  library_dep_index: 4
}
library_dependencies {
  library_index: 22
  library_dep_index: 13
  library_dep_index: 14
  library_dep_index: 23
  library_dep_index: 4
  library_dep_index: 15
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 12
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
}
library_dependencies {
  library_index: 23
  library_dep_index: 13
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 4
  library_dep_index: 15
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 12
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
}
library_dependencies {
  library_index: 24
  library_dep_index: 2
  library_dep_index: 12
  library_dep_index: 25
  library_dep_index: 4
  library_dep_index: 15
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 12
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
}
library_dependencies {
  library_index: 25
  library_dep_index: 2
  library_dep_index: 26
}
library_dependencies {
  library_index: 26
  library_dep_index: 2
}
library_dependencies {
  library_index: 27
  library_dep_index: 12
  library_dep_index: 4
  library_dep_index: 15
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 12
  library_dep_index: 28
  library_dep_index: 29
}
library_dependencies {
  library_index: 28
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 15
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 12
  library_dep_index: 29
  library_dep_index: 27
}
library_dependencies {
  library_index: 29
  library_dep_index: 2
  library_dep_index: 30
  library_dep_index: 23
  library_dep_index: 28
  library_dep_index: 31
  library_dep_index: 4
  library_dep_index: 16
  library_dep_index: 15
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 12
  library_dep_index: 28
  library_dep_index: 27
}
library_dependencies {
  library_index: 30
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 4
  library_dep_index: 8
}
library_dependencies {
  library_index: 31
  library_dep_index: 2
  library_dep_index: 13
  library_dep_index: 15
  library_dep_index: 4
}
library_dependencies {
  library_index: 32
  library_dep_index: 2
  library_dep_index: 10
  library_dep_index: 25
  library_dep_index: 11
}
library_dependencies {
  library_index: 33
  library_dep_index: 2
  library_dep_index: 7
}
library_dependencies {
  library_index: 34
  library_dep_index: 2
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 0
}
library_dependencies {
  library_index: 35
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 7
}
library_dependencies {
  library_index: 36
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 7
}
library_dependencies {
  library_index: 37
  library_dep_index: 2
}
library_dependencies {
  library_index: 38
  library_dep_index: 2
}
library_dependencies {
  library_index: 39
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 40
}
library_dependencies {
  library_index: 40
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 7
}
library_dependencies {
  library_index: 41
  library_dep_index: 2
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 24
  library_dep_index: 25
}
library_dependencies {
  library_index: 42
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 41
}
library_dependencies {
  library_index: 43
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 7
  library_dep_index: 44
  library_dep_index: 45
  library_dep_index: 1
  library_dep_index: 23
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 9
}
library_dependencies {
  library_index: 44
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 40
}
library_dependencies {
  library_index: 45
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 22
  library_dep_index: 28
}
library_dependencies {
  library_index: 46
  library_dep_index: 2
}
library_dependencies {
  library_index: 47
  library_dep_index: 2
  library_dep_index: 7
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 37
  library_dep_index: 11
}
library_dependencies {
  library_index: 48
  library_dep_index: 8
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 54
  library_dep_index: 43
}
library_dependencies {
  library_index: 49
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 33
}
library_dependencies {
  library_index: 50
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 51
  library_dep_index: 45
  library_dep_index: 52
  library_dep_index: 53
}
library_dependencies {
  library_index: 51
  library_dep_index: 2
}
library_dependencies {
  library_index: 52
  library_dep_index: 2
}
library_dependencies {
  library_index: 53
  library_dep_index: 2
}
library_dependencies {
  library_index: 54
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 50
  library_dep_index: 40
  library_dep_index: 44
  library_dep_index: 55
  library_dep_index: 39
  library_dep_index: 56
  library_dep_index: 37
  library_dep_index: 57
  library_dep_index: 58
  library_dep_index: 38
}
library_dependencies {
  library_index: 55
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 40
  library_dep_index: 7
}
library_dependencies {
  library_index: 56
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 40
}
library_dependencies {
  library_index: 57
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 37
}
library_dependencies {
  library_index: 58
  library_dep_index: 2
  library_dep_index: 8
}
library_dependencies {
  library_index: 59
  library_dep_index: 60
  library_dep_index: 61
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 0
  library_dep_index: 62
  library_dep_index: 55
  library_dep_index: 63
  library_dep_index: 8
  library_dep_index: 39
  library_dep_index: 65
  library_dep_index: 9
  library_dep_index: 43
  library_dep_index: 12
  library_dep_index: 66
  library_dep_index: 46
  library_dep_index: 67
  library_dep_index: 35
  library_dep_index: 68
}
library_dependencies {
  library_index: 60
  library_dep_index: 4
  library_dep_index: 5
  library_dep_index: 20
  library_dep_index: 21
}
library_dependencies {
  library_index: 62
  library_dep_index: 2
}
library_dependencies {
  library_index: 63
  library_dep_index: 0
  library_dep_index: 8
  library_dep_index: 64
}
library_dependencies {
  library_index: 65
  library_dep_index: 8
  library_dep_index: 7
  library_dep_index: 50
}
library_dependencies {
  library_index: 66
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 40
  library_dep_index: 7
}
library_dependencies {
  library_index: 67
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 7
}
library_dependencies {
  library_index: 68
  library_dep_index: 2
  library_dep_index: 43
  library_dep_index: 66
  library_dep_index: 8
  library_dep_index: 7
}
library_dependencies {
  library_index: 69
  library_dep_index: 47
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 77
  library_dep_index: 79
  library_dep_index: 80
  library_dep_index: 89
  library_dep_index: 78
  library_dep_index: 91
  library_dep_index: 11
}
library_dependencies {
  library_index: 70
  library_dep_index: 2
  library_dep_index: 30
  library_dep_index: 4
  library_dep_index: 17
  library_dep_index: 71
}
library_dependencies {
  library_index: 71
  library_dep_index: 2
  library_dep_index: 10
  library_dep_index: 30
  library_dep_index: 70
  library_dep_index: 72
  library_dep_index: 11
  library_dep_index: 4
  library_dep_index: 17
  library_dep_index: 70
}
library_dependencies {
  library_index: 72
  library_dep_index: 73
  library_dep_index: 11
  library_dep_index: 74
  library_dep_index: 75
  library_dep_index: 61
  library_dep_index: 76
}
library_dependencies {
  library_index: 77
  library_dep_index: 78
}
library_dependencies {
  library_index: 78
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 43
}
library_dependencies {
  library_index: 79
  library_dep_index: 78
}
library_dependencies {
  library_index: 80
  library_dep_index: 81
  library_dep_index: 77
  library_dep_index: 78
  library_dep_index: 86
  library_dep_index: 88
}
library_dependencies {
  library_index: 81
  library_dep_index: 9
  library_dep_index: 11
  library_dep_index: 22
  library_dep_index: 25
  library_dep_index: 8
  library_dep_index: 82
  library_dep_index: 85
  library_dep_index: 84
  library_dep_index: 8
  library_dep_index: 27
}
library_dependencies {
  library_index: 82
  library_dep_index: 83
  library_dep_index: 84
  library_dep_index: 85
  library_dep_index: 14
}
library_dependencies {
  library_index: 83
  library_dep_index: 2
}
library_dependencies {
  library_index: 84
  library_dep_index: 2
  library_dep_index: 85
}
library_dependencies {
  library_index: 85
  library_dep_index: 2
}
library_dependencies {
  library_index: 86
  library_dep_index: 78
  library_dep_index: 87
}
library_dependencies {
  library_index: 87
  library_dep_index: 78
}
library_dependencies {
  library_index: 88
  library_dep_index: 2
  library_dep_index: 79
  library_dep_index: 78
}
library_dependencies {
  library_index: 89
  library_dep_index: 90
  library_dep_index: 78
  library_dep_index: 91
}
library_dependencies {
  library_index: 90
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 43
  library_dep_index: 78
  library_dep_index: 91
}
library_dependencies {
  library_index: 91
  library_dep_index: 78
}
library_dependencies {
  library_index: 93
  library_dep_index: 92
}
library_dependencies {
  library_index: 95
  library_dep_index: 2
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 47
  dependency_index: 48
  dependency_index: 59
  dependency_index: 69
  dependency_index: 92
  dependency_index: 93
  dependency_index: 94
  dependency_index: 95
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://jitpack.io"
  }
}
