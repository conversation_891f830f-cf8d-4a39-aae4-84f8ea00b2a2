package com.bezruk.qrcodebarcode.utility;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.view.ViewTreeObserver;

import java.lang.ref.WeakReference;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Helper class for performance optimizations
 */
public class PerformanceHelper {
    
    private static final String TAG = "PerformanceHelper";
    private static ExecutorService backgroundExecutor;
    private static Handler mainHandler;
    
    static {
        backgroundExecutor = Executors.newFixedThreadPool(4);
        mainHandler = new Handler(Looper.getMainLooper());
    }
    
    /**
     * Execute task in background thread
     */
    public static void executeInBackground(Runnable task) {
        if (backgroundExecutor != null && !backgroundExecutor.isShutdown()) {
            backgroundExecutor.execute(task);
        }
    }
    
    /**
     * Execute task on main thread
     */
    public static void executeOnMainThread(Runnable task) {
        if (mainHandler != null) {
            mainHandler.post(task);
        }
    }
    
    /**
     * Execute task on main thread with delay
     */
    public static void executeOnMainThreadDelayed(Runnable task, long delayMillis) {
        if (mainHandler != null) {
            mainHandler.postDelayed(task, delayMillis);
        }
    }
    
    /**
     * Optimize bitmap for memory usage
     */
    public static Bitmap optimizeBitmap(Bitmap original, int maxWidth, int maxHeight) {
        if (original == null) return null;
        
        int width = original.getWidth();
        int height = original.getHeight();
        
        if (width <= maxWidth && height <= maxHeight) {
            return original;
        }
        
        float scaleWidth = (float) maxWidth / width;
        float scaleHeight = (float) maxHeight / height;
        float scale = Math.min(scaleWidth, scaleHeight);
        
        int newWidth = Math.round(width * scale);
        int newHeight = Math.round(height * scale);
        
        return Bitmap.createScaledBitmap(original, newWidth, newHeight, true);
    }
    
    /**
     * Recycle bitmap safely
     */
    public static void recycleBitmap(Bitmap bitmap) {
        if (bitmap != null && !bitmap.isRecycled()) {
            bitmap.recycle();
        }
    }
    
    /**
     * Measure view rendering time
     */
    public static void measureViewRenderTime(View view, String tag) {
        if (view == null) return;
        
        final long startTime = System.currentTimeMillis();
        
        view.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                view.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                long renderTime = System.currentTimeMillis() - startTime;
                Log.d(TAG, tag + " render time: " + renderTime + "ms");
            }
        });
    }
    
    /**
     * Memory usage monitoring
     */
    public static void logMemoryUsage(String tag) {
        Runtime runtime = Runtime.getRuntime();
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        long maxMemory = runtime.maxMemory();
        
        Log.d(TAG, String.format("%s - Memory usage: %d MB / %d MB (%.1f%%)", 
            tag, 
            usedMemory / (1024 * 1024), 
            maxMemory / (1024 * 1024),
            (usedMemory * 100.0) / maxMemory));
    }
    
    /**
     * Force garbage collection (use sparingly)
     */
    public static void forceGarbageCollection() {
        System.gc();
        Log.d(TAG, "Forced garbage collection");
    }
    
    /**
     * Check if running on main thread
     */
    public static boolean isMainThread() {
        return Looper.myLooper() == Looper.getMainLooper();
    }
    
    /**
     * Weak reference holder for activities to prevent memory leaks
     */
    public static class WeakActivityReference {
        private WeakReference<Activity> activityRef;
        
        public WeakActivityReference(Activity activity) {
            this.activityRef = new WeakReference<>(activity);
        }
        
        public Activity get() {
            return activityRef != null ? activityRef.get() : null;
        }
        
        public boolean isValid() {
            Activity activity = get();
            return activity != null && !activity.isFinishing() && !activity.isDestroyed();
        }
    }
    
    /**
     * Cleanup resources
     */
    public static void cleanup() {
        if (backgroundExecutor != null && !backgroundExecutor.isShutdown()) {
            backgroundExecutor.shutdown();
        }
        if (mainHandler != null) {
            mainHandler.removeCallbacksAndMessages(null);
        }
    }
    
    /**
     * Initialize performance helper
     */
    public static void initialize() {
        if (backgroundExecutor == null || backgroundExecutor.isShutdown()) {
            backgroundExecutor = Executors.newFixedThreadPool(4);
        }
        if (mainHandler == null) {
            mainHandler = new Handler(Looper.getMainLooper());
        }
    }
    
    /**
     * Get available memory in MB
     */
    public static long getAvailableMemoryMB() {
        Runtime runtime = Runtime.getRuntime();
        return (runtime.maxMemory() - (runtime.totalMemory() - runtime.freeMemory())) / (1024 * 1024);
    }
    
    /**
     * Check if low memory condition
     */
    public static boolean isLowMemory() {
        return getAvailableMemoryMB() < 50; // Less than 50MB available
    }
}
