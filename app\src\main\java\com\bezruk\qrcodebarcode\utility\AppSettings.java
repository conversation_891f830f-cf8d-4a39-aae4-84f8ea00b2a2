package com.bezruk.qrcodebarcode.utility;

import android.content.Context;
import android.content.SharedPreferences;
import android.preference.PreferenceManager;

/**
 * Centralized app settings management
 */
public class AppSettings {
    
    private static final String PREF_CAMERA_FLASH = "camera_flash_enabled";
    private static final String PREF_AUTO_FOCUS = "auto_focus_enabled";
    private static final String PREF_VIBRATION = "vibration_enabled";
    private static final String PREF_SOUND = "sound_enabled";
    private static final String PREF_SAVE_TO_GALLERY = "save_to_gallery";
    private static final String PREF_THEME_MODE = "theme_mode";
    private static final String PREF_SCAN_HISTORY_LIMIT = "scan_history_limit";
    private static final String PREF_AUTO_COPY = "auto_copy_enabled";
    private static final String PREF_SHOW_SCAN_OVERLAY = "show_scan_overlay";
    private static final String PREF_DEFAULT_QR_COLOR = "default_qr_color";
    private static final String PREF_FIRST_LAUNCH = "first_launch";
    private static final String PREF_LAST_VERSION_CODE = "last_version_code";
    
    // Theme modes
    public static final int THEME_SYSTEM = 0;
    public static final int THEME_LIGHT = 1;
    public static final int THEME_DARK = 2;
    
    // Default values
    private static final boolean DEFAULT_FLASH = false;
    private static final boolean DEFAULT_AUTO_FOCUS = true;
    private static final boolean DEFAULT_VIBRATION = true;
    private static final boolean DEFAULT_SOUND = true;
    private static final boolean DEFAULT_SAVE_TO_GALLERY = true;
    private static final int DEFAULT_THEME = THEME_SYSTEM;
    private static final int DEFAULT_HISTORY_LIMIT = 100;
    private static final boolean DEFAULT_AUTO_COPY = false;
    private static final boolean DEFAULT_SHOW_OVERLAY = true;
    private static final int DEFAULT_QR_COLOR = 0xFF000000; // Black
    
    private SharedPreferences prefs;
    private static AppSettings instance;
    
    private AppSettings(Context context) {
        prefs = PreferenceManager.getDefaultSharedPreferences(context.getApplicationContext());
    }
    
    public static synchronized AppSettings getInstance(Context context) {
        if (instance == null) {
            instance = new AppSettings(context);
        }
        return instance;
    }
    
    // Camera settings
    public boolean isCameraFlashEnabled() {
        return prefs.getBoolean(PREF_CAMERA_FLASH, DEFAULT_FLASH);
    }
    
    public void setCameraFlashEnabled(boolean enabled) {
        prefs.edit().putBoolean(PREF_CAMERA_FLASH, enabled).apply();
    }
    
    public boolean isAutoFocusEnabled() {
        return prefs.getBoolean(PREF_AUTO_FOCUS, DEFAULT_AUTO_FOCUS);
    }
    
    public void setAutoFocusEnabled(boolean enabled) {
        prefs.edit().putBoolean(PREF_AUTO_FOCUS, enabled).apply();
    }
    
    // Feedback settings
    public boolean isVibrationEnabled() {
        return prefs.getBoolean(PREF_VIBRATION, DEFAULT_VIBRATION);
    }
    
    public void setVibrationEnabled(boolean enabled) {
        prefs.edit().putBoolean(PREF_VIBRATION, enabled).apply();
    }
    
    public boolean isSoundEnabled() {
        return prefs.getBoolean(PREF_SOUND, DEFAULT_SOUND);
    }
    
    public void setSoundEnabled(boolean enabled) {
        prefs.edit().putBoolean(PREF_SOUND, enabled).apply();
    }
    
    // Storage settings
    public boolean isSaveToGalleryEnabled() {
        return prefs.getBoolean(PREF_SAVE_TO_GALLERY, DEFAULT_SAVE_TO_GALLERY);
    }
    
    public void setSaveToGalleryEnabled(boolean enabled) {
        prefs.edit().putBoolean(PREF_SAVE_TO_GALLERY, enabled).apply();
    }
    
    // Theme settings
    public int getThemeMode() {
        return prefs.getInt(PREF_THEME_MODE, DEFAULT_THEME);
    }
    
    public void setThemeMode(int themeMode) {
        prefs.edit().putInt(PREF_THEME_MODE, themeMode).apply();
    }
    
    // History settings
    public int getScanHistoryLimit() {
        return prefs.getInt(PREF_SCAN_HISTORY_LIMIT, DEFAULT_HISTORY_LIMIT);
    }
    
    public void setScanHistoryLimit(int limit) {
        prefs.edit().putInt(PREF_SCAN_HISTORY_LIMIT, limit).apply();
    }
    
    // Auto copy setting
    public boolean isAutoCopyEnabled() {
        return prefs.getBoolean(PREF_AUTO_COPY, DEFAULT_AUTO_COPY);
    }
    
    public void setAutoCopyEnabled(boolean enabled) {
        prefs.edit().putBoolean(PREF_AUTO_COPY, enabled).apply();
    }
    
    // Scan overlay setting
    public boolean isShowScanOverlayEnabled() {
        return prefs.getBoolean(PREF_SHOW_SCAN_OVERLAY, DEFAULT_SHOW_OVERLAY);
    }
    
    public void setShowScanOverlayEnabled(boolean enabled) {
        prefs.edit().putBoolean(PREF_SHOW_SCAN_OVERLAY, enabled).apply();
    }
    
    // QR code color setting
    public int getDefaultQRColor() {
        return prefs.getInt(PREF_DEFAULT_QR_COLOR, DEFAULT_QR_COLOR);
    }
    
    public void setDefaultQRColor(int color) {
        prefs.edit().putInt(PREF_DEFAULT_QR_COLOR, color).apply();
    }
    
    // App lifecycle settings
    public boolean isFirstLaunch() {
        return prefs.getBoolean(PREF_FIRST_LAUNCH, true);
    }
    
    public void setFirstLaunchCompleted() {
        prefs.edit().putBoolean(PREF_FIRST_LAUNCH, false).apply();
    }
    
    public int getLastVersionCode() {
        return prefs.getInt(PREF_LAST_VERSION_CODE, 0);
    }
    
    public void setLastVersionCode(int versionCode) {
        prefs.edit().putInt(PREF_LAST_VERSION_CODE, versionCode).apply();
    }
    
    // Utility methods
    public void resetToDefaults() {
        prefs.edit()
            .putBoolean(PREF_CAMERA_FLASH, DEFAULT_FLASH)
            .putBoolean(PREF_AUTO_FOCUS, DEFAULT_AUTO_FOCUS)
            .putBoolean(PREF_VIBRATION, DEFAULT_VIBRATION)
            .putBoolean(PREF_SOUND, DEFAULT_SOUND)
            .putBoolean(PREF_SAVE_TO_GALLERY, DEFAULT_SAVE_TO_GALLERY)
            .putInt(PREF_THEME_MODE, DEFAULT_THEME)
            .putInt(PREF_SCAN_HISTORY_LIMIT, DEFAULT_HISTORY_LIMIT)
            .putBoolean(PREF_AUTO_COPY, DEFAULT_AUTO_COPY)
            .putBoolean(PREF_SHOW_SCAN_OVERLAY, DEFAULT_SHOW_OVERLAY)
            .putInt(PREF_DEFAULT_QR_COLOR, DEFAULT_QR_COLOR)
            .apply();
    }
    
    public void clearAllSettings() {
        prefs.edit().clear().apply();
    }
    
    // Export/Import settings (for backup)
    public String exportSettings() {
        StringBuilder sb = new StringBuilder();
        sb.append("flash=").append(isCameraFlashEnabled()).append("\n");
        sb.append("autofocus=").append(isAutoFocusEnabled()).append("\n");
        sb.append("vibration=").append(isVibrationEnabled()).append("\n");
        sb.append("sound=").append(isSoundEnabled()).append("\n");
        sb.append("save_gallery=").append(isSaveToGalleryEnabled()).append("\n");
        sb.append("theme=").append(getThemeMode()).append("\n");
        sb.append("history_limit=").append(getScanHistoryLimit()).append("\n");
        sb.append("auto_copy=").append(isAutoCopyEnabled()).append("\n");
        sb.append("show_overlay=").append(isShowScanOverlayEnabled()).append("\n");
        sb.append("qr_color=").append(getDefaultQRColor()).append("\n");
        return sb.toString();
    }
}
