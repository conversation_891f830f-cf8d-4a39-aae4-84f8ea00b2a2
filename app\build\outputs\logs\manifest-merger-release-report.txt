-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:91:9-99:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:94:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:93:13-64
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:95:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:92:13-62
manifest
ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:2:1-103:12
INJECTED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:2:1-103:12
INJECTED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:2:1-103:12
INJECTED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:2:1-103:12
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\6a153e0be5a372f172b5b708d9293a44\transformed\material-1.10.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\dcc78b2f4a3d879e4e36052f1930563b\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\241e9d5c276921da3f296e4baa64f37e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\1269e761ba2b2764df14d796e474c793\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-ads:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\b544b6c935c841456a5135f6852c436f\transformed\jetified-play-services-ads-22.6.0\AndroidManifest.xml:17:1-25:12
MERGED from [androidx.browser:browser:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\49ae36518fcf1866e9d9d51daa2bfc7e\transformed\browser-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b70d23a24d1a6f8a361ce1de3b1c54c8\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:17:1-56:12
MERGED from [com.github.clans:fab:1.6.4] C:\Users\<USER>\.gradle\caches\transforms-3\14362956b7321eee3001c33cdf6ef430\transformed\jetified-fab-1.6.4\AndroidManifest.xml:2:1-14:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fb49cafff1a3e07b9d521461cce18e18\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:17:1-95:12
MERGED from [com.google.android.gms:play-services-ads-base:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\0bd0a6adc68b8b1bf4b1118b7a808378\transformed\jetified-play-services-ads-base-22.6.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.ump:user-messaging-platform:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2b36755afce5ca95d1b74b658a206ce7\transformed\jetified-user-messaging-platform-2.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b519a920faaec805d6def4cf0a16d5b\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\38d3a8a5e7972e3a45c3f1191aa95b9c\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ede285f672e73ef59c1f7bc9b2e633f9\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\b0a0f412b3e850c2263d22d4bf6b4633\transformed\jetified-play-services-tasks-18.0.1\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\841e3cab8a4af71928abf9f5a0c9ceda\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\4548b7619a7712c79f28a8128ec0e0c4\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\034e53a5369349e96a448cde5bcf995e\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\4a11e6aca8deda0d389cc913a41ef35f\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\13f89a7e108a05276e93b987aef7e57c\transformed\jetified-activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7cf940d8aecd71ce173419862db1d059\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4381fccdde393d19d04161b40313d198\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ccf6a9e0c42fb17a37f359bb12f6567c\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\21dedc24f6ee6d550ce699012c440989\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2252a0ff3ac2470f81c10a7f23733dd1\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c436f6c4987b2b5f2492295ac30cd5d7\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7a572ff4edc0378b5d2b48016d96eedc\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\32c941a3a261d4a55722954517b3f409\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\650519a49e7a6174437614923942058b\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d09301d84fd933a1d774e17906900557\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae509a493480b118a0aac5750246fc28\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\38e8de66ee4c11f758694b174d0400e9\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\63caa8bf6109ecbe6af1511e60399f08\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3663926268b69feaa47957f7c0b6a16a\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\517a712e822896ea7ad2dad9894f94d1\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\1bee4c10944207fa6ddb7ae3b0316bd3\transformed\jetified-lifecycle-service-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\818eda85d3b5ca5901b62e2194b4fd5f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\04a2cc2cf974a3e31486f48a52bf28bd\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\34f9b4270b7c99dcca59829b049b10ab\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\51b62a6c97e776fdd191bf135d7ebb51\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8d855579f340258c3d768fdeeb11fee6\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\3494a56d605fcb3bbfa0fb151ebc1cf4\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\c0b0cf2fba342d13ebac0b7c5ea4e239\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\41c7278328c44568536312b501fc5a8b\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c15fc83dab26ee0f2f8a57d15285d31e\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b8b218ef8ce5149daacd8ff2d969ec40\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\933c4ec1cd017daec41eb61a878c5280\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c0ac89fe8199f06f14827c1abbfd1142\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\78f93d7ef28cd672bda3052197637c47\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e7985d271e77b1c366dbb82aaf93061\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\08fc100efc84a9620b7542fa7648e5ed\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\153182fad44adaed0ba316885bfa8aba\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\47ab4dae8f4577a0d35e59cd24d21777\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5bc9fdabd04994fe779cce5618b535f7\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\77157300835459151e88319959ebe775\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa6a0489060b8ac42de7abd420adb0c\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e1c5158000e85a389defcb4f01b048b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\28c7a9ff679d75eaafebefe4e4c1799f\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\43e211c18de3038f20610673a4c19103\transformed\room-runtime-2.2.5\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4d46c66381fe03d5ba8401bfbeda42c\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1f18028e4c36a0180024c369f5836ece\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7b0913f916526b75716fab079cc1570\transformed\sqlite-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\37ab1ae909e34c7c45d3407cdc79b73c\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:6:5-65
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:22:5-65
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:22:5-65
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:6:22-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:9:5-10:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:10:9-35
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:9:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:11:5-13:40
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:12:9-35
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:13:9-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:11:22-78
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:14:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:14:22-73
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:17:5-66
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:17:22-63
uses-permission#android.permission.CALL_PHONE
ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:20:5-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:20:22-66
uses-permission#android.permission.READ_CONTACTS
ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:23:5-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:23:22-69
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:26:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:26:22-73
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:27:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:27:22-73
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:30:5-67
MERGED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\841e3cab8a4af71928abf9f5a0c9ceda\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\841e3cab8a4af71928abf9f5a0c9ceda\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:23:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:30:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:31:5-79
MERGED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\841e3cab8a4af71928abf9f5a0c9ceda\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\841e3cab8a4af71928abf9f5a0c9ceda\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:31:22-76
uses-permission#com.android.vending.BILLING
ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:34:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:34:22-64
uses-feature#android.hardware.camera
ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:37:5-39:35
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:25:5-27:36
	android:required
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:39:9-32
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:38:9-47
uses-feature#android.hardware.camera.autofocus
ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:40:5-42:36
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:33:5-35:36
	android:required
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:42:9-33
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:41:9-57
uses-feature#android.hardware.camera.flash
ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:43:5-45:36
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
	android:required
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:45:9-33
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:44:9-53
application
ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:47:5-101:19
INJECTED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:47:5-101:19
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\6a153e0be5a372f172b5b708d9293a44\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\6a153e0be5a372f172b5b708d9293a44\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\241e9d5c276921da3f296e4baa64f37e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\241e9d5c276921da3f296e4baa64f37e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:46:5-54:19
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:46:5-54:19
MERGED from [com.github.clans:fab:1.6.4] C:\Users\<USER>\.gradle\caches\transforms-3\14362956b7321eee3001c33cdf6ef430\transformed\jetified-fab-1.6.4\AndroidManifest.xml:12:5-20
MERGED from [com.github.clans:fab:1.6.4] C:\Users\<USER>\.gradle\caches\transforms-3\14362956b7321eee3001c33cdf6ef430\transformed\jetified-fab-1.6.4\AndroidManifest.xml:12:5-20
MERGED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:53:5-93:19
MERGED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:53:5-93:19
MERGED from [com.google.android.gms:play-services-ads-base:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\0bd0a6adc68b8b1bf4b1118b7a808378\transformed\jetified-play-services-ads-base-22.6.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-base:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\0bd0a6adc68b8b1bf4b1118b7a808378\transformed\jetified-play-services-ads-base-22.6.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b519a920faaec805d6def4cf0a16d5b\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b519a920faaec805d6def4cf0a16d5b\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\38d3a8a5e7972e3a45c3f1191aa95b9c\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\38d3a8a5e7972e3a45c3f1191aa95b9c\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ede285f672e73ef59c1f7bc9b2e633f9\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ede285f672e73ef59c1f7bc9b2e633f9\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\b0a0f412b3e850c2263d22d4bf6b4633\transformed\jetified-play-services-tasks-18.0.1\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\b0a0f412b3e850c2263d22d4bf6b4633\transformed\jetified-play-services-tasks-18.0.1\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\4548b7619a7712c79f28a8128ec0e0c4\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\4548b7619a7712c79f28a8128ec0e0c4\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\034e53a5369349e96a448cde5bcf995e\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\034e53a5369349e96a448cde5bcf995e\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4381fccdde393d19d04161b40313d198\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4381fccdde393d19d04161b40313d198\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\63caa8bf6109ecbe6af1511e60399f08\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\63caa8bf6109ecbe6af1511e60399f08\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\818eda85d3b5ca5901b62e2194b4fd5f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\818eda85d3b5ca5901b62e2194b4fd5f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\08fc100efc84a9620b7542fa7648e5ed\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\08fc100efc84a9620b7542fa7648e5ed\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e1c5158000e85a389defcb4f01b048b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e1c5158000e85a389defcb4f01b048b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\43e211c18de3038f20610673a4c19103\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\43e211c18de3038f20610673a4c19103\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:57:9-52
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:53:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:52:9-41
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:55:9-43
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:50:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:58:9-29
	android:largeHeap
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:56:9-33
	android:icon
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:51:9-45
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:48:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:54:9-40
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:49:9-65
activity#com.bezruk.qrcodebarcode.activity.SplashActivity
ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:60:9-70:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:64:13-49
	android:label
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:62:13-45
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:65:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:63:13-56
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:61:13-52
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:66:13-69:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:67:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:67:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:68:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:68:27-74
activity#com.bezruk.qrcodebarcode.activity.MainActivity
ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:72:9-77:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:74:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:76:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:75:13-56
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:73:13-50
activity#com.bezruk.qrcodebarcode.activity.ResultActivity
ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:79:9-83:39
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:81:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:83:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:82:13-56
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:80:13-52
meta-data#com.google.android.gms.ads.APPLICATION_ID
ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:86:9-88:69
	android:value
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:88:13-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:87:13-69
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:96:13-98:54
	android:resource
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:98:17-51
	android:name
		ADDED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:97:17-67
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\6a153e0be5a372f172b5b708d9293a44\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-3\6a153e0be5a372f172b5b708d9293a44\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\dcc78b2f4a3d879e4e36052f1930563b\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\dcc78b2f4a3d879e4e36052f1930563b\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\241e9d5c276921da3f296e4baa64f37e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\241e9d5c276921da3f296e4baa64f37e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\1269e761ba2b2764df14d796e474c793\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\1269e761ba2b2764df14d796e474c793\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\b544b6c935c841456a5135f6852c436f\transformed\jetified-play-services-ads-22.6.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\b544b6c935c841456a5135f6852c436f\transformed\jetified-play-services-ads-22.6.0\AndroidManifest.xml:21:5-23:52
MERGED from [androidx.browser:browser:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\49ae36518fcf1866e9d9d51daa2bfc7e\transformed\browser-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\49ae36518fcf1866e9d9d51daa2bfc7e\transformed\browser-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b70d23a24d1a6f8a361ce1de3b1c54c8\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b70d23a24d1a6f8a361ce1de3b1c54c8\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.clans:fab:1.6.4] C:\Users\<USER>\.gradle\caches\transforms-3\14362956b7321eee3001c33cdf6ef430\transformed\jetified-fab-1.6.4\AndroidManifest.xml:8:5-10:41
MERGED from [com.github.clans:fab:1.6.4] C:\Users\<USER>\.gradle\caches\transforms-3\14362956b7321eee3001c33cdf6ef430\transformed\jetified-fab-1.6.4\AndroidManifest.xml:8:5-10:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fb49cafff1a3e07b9d521461cce18e18\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fb49cafff1a3e07b9d521461cce18e18\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-base:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\0bd0a6adc68b8b1bf4b1118b7a808378\transformed\jetified-play-services-ads-base-22.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-base:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\0bd0a6adc68b8b1bf4b1118b7a808378\transformed\jetified-play-services-ads-base-22.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.ump:user-messaging-platform:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2b36755afce5ca95d1b74b658a206ce7\transformed\jetified-user-messaging-platform-2.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.ump:user-messaging-platform:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2b36755afce5ca95d1b74b658a206ce7\transformed\jetified-user-messaging-platform-2.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b519a920faaec805d6def4cf0a16d5b\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b519a920faaec805d6def4cf0a16d5b\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\38d3a8a5e7972e3a45c3f1191aa95b9c\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\38d3a8a5e7972e3a45c3f1191aa95b9c\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ede285f672e73ef59c1f7bc9b2e633f9\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ede285f672e73ef59c1f7bc9b2e633f9\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\b0a0f412b3e850c2263d22d4bf6b4633\transformed\jetified-play-services-tasks-18.0.1\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\b0a0f412b3e850c2263d22d4bf6b4633\transformed\jetified-play-services-tasks-18.0.1\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\841e3cab8a4af71928abf9f5a0c9ceda\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\841e3cab8a4af71928abf9f5a0c9ceda\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\4548b7619a7712c79f28a8128ec0e0c4\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\4548b7619a7712c79f28a8128ec0e0c4\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\034e53a5369349e96a448cde5bcf995e\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\034e53a5369349e96a448cde5bcf995e\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\4a11e6aca8deda0d389cc913a41ef35f\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\4a11e6aca8deda0d389cc913a41ef35f\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\13f89a7e108a05276e93b987aef7e57c\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\13f89a7e108a05276e93b987aef7e57c\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7cf940d8aecd71ce173419862db1d059\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7cf940d8aecd71ce173419862db1d059\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4381fccdde393d19d04161b40313d198\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4381fccdde393d19d04161b40313d198\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ccf6a9e0c42fb17a37f359bb12f6567c\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ccf6a9e0c42fb17a37f359bb12f6567c\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\21dedc24f6ee6d550ce699012c440989\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\21dedc24f6ee6d550ce699012c440989\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2252a0ff3ac2470f81c10a7f23733dd1\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2252a0ff3ac2470f81c10a7f23733dd1\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c436f6c4987b2b5f2492295ac30cd5d7\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c436f6c4987b2b5f2492295ac30cd5d7\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7a572ff4edc0378b5d2b48016d96eedc\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7a572ff4edc0378b5d2b48016d96eedc\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\32c941a3a261d4a55722954517b3f409\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\32c941a3a261d4a55722954517b3f409\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\650519a49e7a6174437614923942058b\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\650519a49e7a6174437614923942058b\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d09301d84fd933a1d774e17906900557\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d09301d84fd933a1d774e17906900557\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae509a493480b118a0aac5750246fc28\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae509a493480b118a0aac5750246fc28\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\38e8de66ee4c11f758694b174d0400e9\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\38e8de66ee4c11f758694b174d0400e9\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\63caa8bf6109ecbe6af1511e60399f08\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\63caa8bf6109ecbe6af1511e60399f08\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3663926268b69feaa47957f7c0b6a16a\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3663926268b69feaa47957f7c0b6a16a\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\517a712e822896ea7ad2dad9894f94d1\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\517a712e822896ea7ad2dad9894f94d1\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\1bee4c10944207fa6ddb7ae3b0316bd3\transformed\jetified-lifecycle-service-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\1bee4c10944207fa6ddb7ae3b0316bd3\transformed\jetified-lifecycle-service-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\818eda85d3b5ca5901b62e2194b4fd5f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\818eda85d3b5ca5901b62e2194b4fd5f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\04a2cc2cf974a3e31486f48a52bf28bd\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\04a2cc2cf974a3e31486f48a52bf28bd\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\34f9b4270b7c99dcca59829b049b10ab\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\34f9b4270b7c99dcca59829b049b10ab\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\51b62a6c97e776fdd191bf135d7ebb51\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\51b62a6c97e776fdd191bf135d7ebb51\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8d855579f340258c3d768fdeeb11fee6\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\8d855579f340258c3d768fdeeb11fee6\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\3494a56d605fcb3bbfa0fb151ebc1cf4\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\3494a56d605fcb3bbfa0fb151ebc1cf4\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\c0b0cf2fba342d13ebac0b7c5ea4e239\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\c0b0cf2fba342d13ebac0b7c5ea4e239\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\41c7278328c44568536312b501fc5a8b\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\41c7278328c44568536312b501fc5a8b\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c15fc83dab26ee0f2f8a57d15285d31e\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c15fc83dab26ee0f2f8a57d15285d31e\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b8b218ef8ce5149daacd8ff2d969ec40\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b8b218ef8ce5149daacd8ff2d969ec40\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\933c4ec1cd017daec41eb61a878c5280\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\933c4ec1cd017daec41eb61a878c5280\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c0ac89fe8199f06f14827c1abbfd1142\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c0ac89fe8199f06f14827c1abbfd1142\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\78f93d7ef28cd672bda3052197637c47\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\78f93d7ef28cd672bda3052197637c47\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e7985d271e77b1c366dbb82aaf93061\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5e7985d271e77b1c366dbb82aaf93061\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\08fc100efc84a9620b7542fa7648e5ed\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\08fc100efc84a9620b7542fa7648e5ed\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\153182fad44adaed0ba316885bfa8aba\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\153182fad44adaed0ba316885bfa8aba\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\47ab4dae8f4577a0d35e59cd24d21777\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\47ab4dae8f4577a0d35e59cd24d21777\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5bc9fdabd04994fe779cce5618b535f7\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5bc9fdabd04994fe779cce5618b535f7\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\77157300835459151e88319959ebe775\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\77157300835459151e88319959ebe775\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa6a0489060b8ac42de7abd420adb0c\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa6a0489060b8ac42de7abd420adb0c\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e1c5158000e85a389defcb4f01b048b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e1c5158000e85a389defcb4f01b048b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\28c7a9ff679d75eaafebefe4e4c1799f\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\28c7a9ff679d75eaafebefe4e4c1799f\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\43e211c18de3038f20610673a4c19103\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\43e211c18de3038f20610673a4c19103\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4d46c66381fe03d5ba8401bfbeda42c\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4d46c66381fe03d5ba8401bfbeda42c\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1f18028e4c36a0180024c369f5836ece\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1f18028e4c36a0180024c369f5836ece\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7b0913f916526b75716fab079cc1570\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a7b0913f916526b75716fab079cc1570\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\37ab1ae909e34c7c45d3407cdc79b73c\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\37ab1ae909e34c7c45d3407cdc79b73c\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
	tools:overrideLibrary
		ADDED from [com.google.android.gms:play-services-ads:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\b544b6c935c841456a5135f6852c436f\transformed\jetified-play-services-ads-22.6.0\AndroidManifest.xml:23:9-49
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml
uses-feature#android.hardware.camera.front
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
uses-feature#android.hardware.screen.landscape
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
uses-feature#android.hardware.wifi
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
activity#com.journeyapps.barcodescanner.CaptureActivity
ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
	android:screenOrientation
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
	android:clearTaskOnLaunch
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
	android:stateNotNeeded
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
	android:windowSoftInputMode
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
	android:theme
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b519a920faaec805d6def4cf0a16d5b\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6b519a920faaec805d6def4cf0a16d5b\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\841e3cab8a4af71928abf9f5a0c9ceda\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\841e3cab8a4af71928abf9f5a0c9ceda\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:26:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:27:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:27:22-79
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:28:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:28:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_TOPICS
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:29:5-83
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:29:22-80
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:30:5-32:31
REJECTED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:27:5-81
	tools:node
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:32:9-28
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:31:9-65
queries
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:35:5-51:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:38:9-44:18
action#android.intent.action.VIEW
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:39:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:39:21-62
category#android.intent.category.BROWSABLE
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:41:13-74
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:41:23-71
data
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:43:13-44
	android:scheme
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:43:19-41
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:47:9-49:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:48:13-90
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:48:21-87
activity#com.google.android.gms.ads.AdActivity
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:56:9-61:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:59:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:61:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:58:13-122
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:60:13-61
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:57:13-65
provider#com.google.android.gms.ads.MobileAdsInitProvider
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:63:9-68:43
	android:authorities
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:65:13-73
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:66:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:68:13-40
	android:initOrder
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:67:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:64:13-76
service#com.google.android.gms.ads.AdService
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:70:9-74:43
	android:enabled
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:72:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:73:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:74:13-40
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:71:13-64
activity#com.google.android.gms.ads.OutOfContextTestingActivity
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:76:9-80:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:79:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:80:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:78:13-122
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:77:13-82
activity#com.google.android.gms.ads.NotificationHandlerActivity
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:81:9-88:43
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:83:13-46
	android:launchMode
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:85:13-44
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:84:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:88:13-40
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:87:13-72
	android:taskAffinity
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:86:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:82:13-82
property
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:90:9-92:62
	android:resource
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:92:13-59
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:91:13-65
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ede285f672e73ef59c1f7bc9b2e633f9\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ede285f672e73ef59c1f7bc9b2e633f9\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ede285f672e73ef59c1f7bc9b2e633f9\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ede285f672e73ef59c1f7bc9b2e633f9\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:20:19-85
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\841e3cab8a4af71928abf9f5a0c9ceda\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\841e3cab8a4af71928abf9f5a0c9ceda\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:22-65
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\034e53a5369349e96a448cde5bcf995e\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\034e53a5369349e96a448cde5bcf995e\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\034e53a5369349e96a448cde5bcf995e\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4381fccdde393d19d04161b40313d198\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\818eda85d3b5ca5901b62e2194b4fd5f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\818eda85d3b5ca5901b62e2194b4fd5f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e1c5158000e85a389defcb4f01b048b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e1c5158000e85a389defcb4f01b048b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4381fccdde393d19d04161b40313d198\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4381fccdde393d19d04161b40313d198\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4381fccdde393d19d04161b40313d198\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4381fccdde393d19d04161b40313d198\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4381fccdde393d19d04161b40313d198\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4381fccdde393d19d04161b40313d198\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4381fccdde393d19d04161b40313d198\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\63caa8bf6109ecbe6af1511e60399f08\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\63caa8bf6109ecbe6af1511e60399f08\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\63caa8bf6109ecbe6af1511e60399f08\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\818eda85d3b5ca5901b62e2194b4fd5f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\818eda85d3b5ca5901b62e2194b4fd5f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\818eda85d3b5ca5901b62e2194b4fd5f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.bezruk.qrcodebarcode.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.bezruk.qrcodebarcode.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\43e211c18de3038f20610673a4c19103\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\43e211c18de3038f20610673a4c19103\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\43e211c18de3038f20610673a4c19103\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\43e211c18de3038f20610673a4c19103\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
