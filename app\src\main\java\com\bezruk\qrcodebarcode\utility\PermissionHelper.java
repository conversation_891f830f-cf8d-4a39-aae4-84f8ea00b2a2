package com.bezruk.qrcodebarcode.utility;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import com.karumi.dexter.Dexter;
import com.karumi.dexter.MultiplePermissionsReport;
import com.karumi.dexter.PermissionToken;
import com.karumi.dexter.listener.PermissionRequest;
import com.karumi.dexter.listener.multi.MultiplePermissionsListener;
import java.util.ArrayList;
import java.util.List;

/**
 * Helper class for managing permissions in a modern Android way
 */
public class PermissionHelper {

    public interface PermissionCallback {
        void onPermissionGranted();
        void onPermissionDenied();
    }

    /**
     * Check and request camera permission
     */
    public static void requestCameraPermission(Activity activity, PermissionCallback callback) {
        Dexter.withActivity(activity)
                .withPermission(Manifest.permission.CAMERA)
                .withListener(new com.karumi.dexter.listener.single.PermissionListener() {
                    @Override
                    public void onPermissionGranted(com.karumi.dexter.listener.single.PermissionGrantedResponse response) {
                        callback.onPermissionGranted();
                    }

                    @Override
                    public void onPermissionDenied(com.karumi.dexter.listener.single.PermissionDeniedResponse response) {
                        callback.onPermissionDenied();
                    }

                    @Override
                    public void onPermissionRationaleShouldBeShown(com.karumi.dexter.listener.single.PermissionRequest permission, PermissionToken token) {
                        token.continuePermissionRequest();
                    }
                }).check();
    }

    /**
     * Check and request storage permissions based on Android version
     */
    public static void requestStoragePermissions(Activity activity, PermissionCallback callback) {
        List<String> permissions = new ArrayList<>();
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ - Use granular media permissions
            permissions.add(Manifest.permission.READ_MEDIA_IMAGES);
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // Android 6+ - Use legacy storage permissions
            permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE);
            if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.Q) {
                permissions.add(Manifest.permission.WRITE_EXTERNAL_STORAGE);
            }
        }

        if (permissions.isEmpty()) {
            callback.onPermissionGranted();
            return;
        }

        Dexter.withActivity(activity)
                .withPermissions(permissions)
                .withListener(new MultiplePermissionsListener() {
                    @Override
                    public void onPermissionsChecked(MultiplePermissionsReport report) {
                        if (report.areAllPermissionsGranted()) {
                            callback.onPermissionGranted();
                        } else {
                            callback.onPermissionDenied();
                        }
                    }

                    @Override
                    public void onPermissionRationaleShouldBeShown(List<PermissionRequest> permissions, PermissionToken token) {
                        token.continuePermissionRequest();
                    }
                }).check();
    }

    /**
     * Check if camera permission is granted
     */
    public static boolean isCameraPermissionGranted(Context context) {
        return ContextCompat.checkSelfPermission(context, Manifest.permission.CAMERA) 
                == PackageManager.PERMISSION_GRANTED;
    }

    /**
     * Check if storage permissions are granted based on Android version
     */
    public static boolean isStoragePermissionGranted(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            return ContextCompat.checkSelfPermission(context, Manifest.permission.READ_MEDIA_IMAGES) 
                    == PackageManager.PERMISSION_GRANTED;
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            boolean readGranted = ContextCompat.checkSelfPermission(context, Manifest.permission.READ_EXTERNAL_STORAGE) 
                    == PackageManager.PERMISSION_GRANTED;
            if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.Q) {
                boolean writeGranted = ContextCompat.checkSelfPermission(context, Manifest.permission.WRITE_EXTERNAL_STORAGE) 
                        == PackageManager.PERMISSION_GRANTED;
                return readGranted && writeGranted;
            }
            return readGranted;
        }
        return true; // No permissions needed for older versions
    }

    /**
     * Check if phone permission is granted
     */
    public static boolean isPhonePermissionGranted(Context context) {
        return ContextCompat.checkSelfPermission(context, Manifest.permission.CALL_PHONE) 
                == PackageManager.PERMISSION_GRANTED;
    }

    /**
     * Request phone permission
     */
    public static void requestPhonePermission(Activity activity, PermissionCallback callback) {
        Dexter.withActivity(activity)
                .withPermission(Manifest.permission.CALL_PHONE)
                .withListener(new com.karumi.dexter.listener.single.PermissionListener() {
                    @Override
                    public void onPermissionGranted(com.karumi.dexter.listener.single.PermissionGrantedResponse response) {
                        callback.onPermissionGranted();
                    }

                    @Override
                    public void onPermissionDenied(com.karumi.dexter.listener.single.PermissionDeniedResponse response) {
                        callback.onPermissionDenied();
                    }

                    @Override
                    public void onPermissionRationaleShouldBeShown(com.karumi.dexter.listener.single.PermissionRequest permission, PermissionToken token) {
                        token.continuePermissionRequest();
                    }
                }).check();
    }
}
