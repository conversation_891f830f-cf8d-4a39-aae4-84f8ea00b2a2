<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    app:layout_behavior="@string/appbar_scrolling_view_behavior">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/resultIcon"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentEnd="true"
                    android:layout_gravity="top|right"
                    android:layout_marginLeft="5dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:padding="4dp"
                    app:srcCompat="@drawable/ic_plain_text"
                    app:tint="@color/colorPrimary" />

                <TextView
                    android:id="@+id/scanned_result_tile"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="10dp"
                    android:text="@string/scanned_result"
                    android:textColor="@color/colorPrimary"
                    android:textSize="20sp"
                    android:textStyle="bold" />

            </LinearLayout>

            <TextView
                android:id="@+id/result"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:background="@color/white"
                android:linksClickable="true"
                android:paddingLeft="15dp"
                android:paddingTop="10dp"
                android:paddingRight="15dp"
                android:paddingBottom="10dp"
                android:text="@string/result"
                android:textSize="18sp" />

            <LinearLayout
                android:id="@+id/result_btns_layout"
                android:layout_width="match_parent"
                android:layout_height="75dp"
                android:layout_marginTop="-3dp"
                android:gravity="center_vertical|center"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/copy_result_btn"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_margin="3dp"
                    android:background="@color/colorAccent"
                    android:clickable="true"
                    android:padding="7dp"
                    app:srcCompat="@drawable/ic_copy_white" />

                <ImageView
                    android:id="@+id/share_result_btn"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_margin="3dp"
                    android:clickable="true"
                    android:padding="7dp"
                    android:visibility="visible"
                    android:background="@color/colorAccent"
                    app:srcCompat="@drawable/ic_share" />

                <ImageView
                    android:id="@+id/action1_result_btn"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_margin="3dp"
                    android:clickable="true"
                    android:padding="7dp"
                    android:visibility="visible"
                    android:background="@color/colorAccent"
                    app:srcCompat="@drawable/ic_contact_white" />

                <ImageView
                    android:id="@+id/action2_result_btn"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_margin="3dp"
                    android:clickable="true"
                    android:padding="7dp"
                    android:visibility="visible"
                    android:background="@color/colorAccent"
                    app:srcCompat="@drawable/ic_call_white" />

                <ImageView
                    android:id="@+id/action3_result_btn"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_margin="3dp"
                    android:clickable="true"
                    android:padding="7dp"
                    android:visibility="visible"
                    android:background="@color/colorAccent"
                    app:srcCompat="@drawable/ic_web_white" />

                <ImageView
                    android:id="@+id/action4_result_btn"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_margin="3dp"
                    android:clickable="true"
                    android:padding="7dp"
                    android:visibility="visible"
                    android:background="@color/colorAccent"
                    app:srcCompat="@drawable/ic_email_white" />
            </LinearLayout>

            <TextView
                android:id="@+id/scanned_result_type_of_code"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:layout_marginBottom="10dp"
                android:text="@string/scanned_type_qrcode"
                android:textAlignment="center"
                android:textSize="20sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/result_qr_code_img"
                android:layout_width="230dp"
                android:layout_height="200dp"
                android:layout_weight="0"
                android:scaleType="fitCenter"
                app:srcCompat="@drawable/ic_qr_placeholder" />

            <LinearLayout
                android:id="@+id/qr_code_result_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical|center"
                android:orientation="horizontal">

            </LinearLayout>

            <LinearLayout
                android:id="@+id/btn_for_qr_code_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:gravity="left|center"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/color_of_result_qrcode_btn"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_marginTop="3dp"
                    android:layout_marginRight="5dp"
                    android:layout_marginBottom="3dp"
                    android:background="@color/colorAccent"
                    android:clickable="true"
                    android:padding="7dp"
                    android:visibility="visible"
                    app:srcCompat="@drawable/ic_color" />

                <ImageView
                    android:id="@+id/save_of_result_qrcode_btn"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_marginTop="3dp"
                    android:layout_marginRight="5dp"
                    android:layout_marginBottom="3dp"
                    android:background="@color/colorAccent"
                    android:clickable="true"
                    android:padding="7dp"
                    android:visibility="visible"
                    app:srcCompat="@drawable/ic_save" />

                <ImageView
                    android:id="@+id/share_of_result_qrcode_btn"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_marginTop="3dp"
                    android:layout_marginRight="5dp"
                    android:layout_marginBottom="3dp"
                    android:background="@color/colorAccent"
                    android:clickable="true"
                    android:padding="7dp"
                    android:visibility="visible"
                    app:srcCompat="@drawable/ic_share" />
            </LinearLayout>

            <com.google.android.gms.ads.AdView
                android:id="@+id/adViewResult"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                app:adSize="LARGE_BANNER"
                app:adUnitId="ca-app-pub-3940256099942544/6300978111" />
        </LinearLayout>
    </ScrollView>



</RelativeLayout>