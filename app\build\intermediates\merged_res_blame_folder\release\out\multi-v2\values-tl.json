{"logs": [{"outputFile": "com.bezruk.qrcodebarcode.app-mergeReleaseResources-41:/values-tl/values-tl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6a153e0be5a372f172b5b708d9293a44\\transformed\\material-1.10.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,280,364,444,530,627,717,822,958,1043,1108,1207,1275,1334,1423,1491,1558,1621,1696,1764,1818,1938,1996,2058,2112,2187,2329,2419,2504,2649,2733,2816,2962,3058,3135,3193,3244,3310,3384,3462,3553,3639,3713,3792,3865,3937,4053,4157,4230,4329,4429,4503,4578,4685,4737,4826,4893,4984,5078,5140,5204,5267,5337,5456,5561,5670,5770,5832,5887", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,83,79,85,96,89,104,135,84,64,98,67,58,88,67,66,62,74,67,53,119,57,61,53,74,141,89,84,144,83,82,145,95,76,57,50,65,73,77,90,85,73,78,72,71,115,103,72,98,99,73,74,106,51,88,66,90,93,61,63,62,69,118,104,108,99,61,54,84", "endOffsets": "275,359,439,525,622,712,817,953,1038,1103,1202,1270,1329,1418,1486,1553,1616,1691,1759,1813,1933,1991,2053,2107,2182,2324,2414,2499,2644,2728,2811,2957,3053,3130,3188,3239,3305,3379,3457,3548,3634,3708,3787,3860,3932,4048,4152,4225,4324,4424,4498,4573,4680,4732,4821,4888,4979,5073,5135,5199,5262,5332,5451,5556,5665,5765,5827,5882,5967"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,60,61,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3070,3154,3234,3320,3417,3507,3612,3748,6298,6363,6783,6851,6910,6999,7067,7134,7197,7272,7340,7394,7514,7572,7634,7688,7763,7905,7995,8080,8225,8309,8392,8538,8634,8711,8769,8820,8886,8960,9038,9129,9215,9289,9368,9441,9513,9629,9733,9806,9905,10005,10079,10154,10261,10313,10402,10469,10560,10654,10716,10780,10843,10913,11032,11137,11246,11346,11408,12520", "endLines": "5,33,34,35,36,37,38,39,40,60,61,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,135", "endColumns": "12,83,79,85,96,89,104,135,84,64,98,67,58,88,67,66,62,74,67,53,119,57,61,53,74,141,89,84,144,83,82,145,95,76,57,50,65,73,77,90,85,73,78,72,71,115,103,72,98,99,73,74,106,51,88,66,90,93,61,63,62,69,118,104,108,99,61,54,84", "endOffsets": "325,3149,3229,3315,3412,3502,3607,3743,3828,6358,6457,6846,6905,6994,7062,7129,7192,7267,7335,7389,7509,7567,7629,7683,7758,7900,7990,8075,8220,8304,8387,8533,8629,8706,8764,8815,8881,8955,9033,9124,9210,9284,9363,9436,9508,9624,9728,9801,9900,10000,10074,10149,10256,10308,10397,10464,10555,10649,10711,10775,10838,10908,11027,11132,11241,11341,11403,11458,12600"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b544b6c935c841456a5135f6852c436f\\transformed\\jetified-play-services-ads-22.6.0\\res\\values-tl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,245,294,350,415,486,598,673,770,820,941,1002,1120,1204,1250,1342,1379,1416,1473,1554,1596", "endColumns": "45,48,55,64,70,111,74,96,49,120,60,117,83,45,91,36,36,56,80,41,55", "endOffsets": "244,293,349,414,485,597,672,769,819,940,1001,1119,1203,1249,1341,1378,1415,1472,1553,1595,1651"}, "to": {"startLines": "122,123,124,125,126,127,128,129,130,131,132,133,134,136,137,138,139,140,141,142,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11463,11513,11566,11626,11695,11770,11886,11965,12066,12120,12245,12310,12432,12605,12655,12751,12792,12833,12894,12979,13211", "endColumns": "49,52,59,68,74,115,78,100,53,124,64,121,87,49,95,40,40,60,84,45,59", "endOffsets": "11508,11561,11621,11690,11765,11881,11960,12061,12115,12240,12305,12427,12515,12650,12746,12787,12828,12889,12974,13020,13266"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1269e761ba2b2764df14d796e474c793\\transformed\\appcompat-1.6.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,2845", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,2925"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,143", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "330,441,549,662,750,856,971,1051,1128,1219,1312,1407,1501,1601,1694,1789,1883,1974,2065,2149,2258,2368,2469,2579,2697,2805,2968,13025", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "436,544,657,745,851,966,1046,1123,1214,1307,1402,1496,1596,1689,1784,1878,1969,2060,2144,2253,2363,2464,2574,2692,2800,2963,3065,13105"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\75919e89ab9acfe92b320b18b5eba894\\transformed\\core-1.9.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "144", "startColumns": "4", "startOffsets": "13110", "endColumns": "100", "endOffsets": "13206"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\49ae36518fcf1866e9d9d51daa2bfc7e\\transformed\\browser-1.7.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,263,374", "endColumns": "102,104,110,104", "endOffsets": "153,258,369,474"}, "to": {"startLines": "59,62,63,64", "startColumns": "4,4,4,4", "startOffsets": "6195,6462,6567,6678", "endColumns": "102,104,110,104", "endOffsets": "6293,6562,6673,6778"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ede285f672e73ef59c1f7bc9b2e633f9\\transformed\\jetified-play-services-base-18.0.0\\res\\values-tl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,468,602,707,861,993,1111,1220,1395,1498,1672,1806,1964,2139,2203,2265", "endColumns": "102,171,133,104,153,131,117,108,174,102,173,133,157,174,63,61,76", "endOffsets": "295,467,601,706,860,992,1110,1219,1394,1497,1671,1805,1963,2138,2202,2264,2341"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3833,3940,4116,4254,4363,4521,4657,4779,5037,5216,5323,5501,5639,5801,5980,6048,6114", "endColumns": "106,175,137,108,157,135,121,112,178,106,177,137,161,178,67,65,80", "endOffsets": "3935,4111,4249,4358,4516,4652,4774,4887,5211,5318,5496,5634,5796,5975,6043,6109,6190"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\034e53a5369349e96a448cde5bcf995e\\transformed\\jetified-play-services-basement-18.2.0\\res\\values-tl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "4892", "endColumns": "144", "endOffsets": "5032"}}]}]}