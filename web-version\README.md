# 🌐 QR Scanner MAX - Web Version

نسخة ويب من تطبيق QR Scanner MAX تعمل في متصفح Google Chrome وجميع المتصفحات الحديثة.

## ✨ المميزات

### 📱 **مسح رموز QR**
- مسح سريع ودقيق للرموز
- دعم جميع أنواع رموز QR
- تبديل بين الكاميرا الأمامية والخلفية
- كشف تلقائي للروابط والإيميلات وأرقام الهواتف

### 🎨 **إنشاء رموز QR**
- إنشاء رموز QR مخصصة
- تحميل الرموز كصور PNG
- جودة عالية وحجم قابل للتخصيص

### 🚀 **Progressive Web App (PWA)**
- يعمل بدون إنترنت بعد التحميل الأول
- يمكن تثبيته على الهاتف كتطبيق
- واجهة سريعة ومتجاوبة

### 🔧 **ميزات إضافية**
- نسخ النتائج للحافظة
- مشاركة النتائج
- اختصارات لوحة المفاتيح
- أصوات وإهتزاز عند المسح

## 🚀 طرق التشغيل

### 1. **الطريقة السريعة - Python Server**
```bash
cd web-version
python server.py
```
سيفتح التطبيق تلقائياً في المتصفح على `http://localhost:8080`

### 2. **خادم HTTP بسيط**
```bash
cd web-version
python -m http.server 8080
```
ثم افتح `http://localhost:8080` في Chrome

### 3. **Node.js Server**
```bash
cd web-version
npx http-server -p 8080 -c-1
```

### 4. **Live Server (VS Code)**
- افتح مجلد `web-version` في VS Code
- استخدم امتداد Live Server
- انقر بالزر الأيمن على `index.html` واختر "Open with Live Server"

## 📱 التثبيت كتطبيق

### على الهاتف:
1. افتح الرابط في Chrome
2. انقر على قائمة Chrome (⋮)
3. اختر "Add to Home screen"
4. اتبع التعليمات

### على الكمبيوتر:
1. افتح الرابط في Chrome
2. انقر على أيقونة التثبيت في شريط العناوين
3. أو اذهب إلى Settings > Install QR Scanner MAX

## ⌨️ اختصارات لوحة المفاتيح

- `Ctrl + S` - بدء/إيقاف المسح
- `Ctrl + G` - إنشاء رمز QR
- `Ctrl + C` - نسخ النتيجة
- `Enter` - إنشاء QR (في حقل النص)

## 🔒 الأمان والخصوصية

- **لا يتم حفظ البيانات**: جميع العمليات تتم محلياً في المتصفح
- **لا توجد خوادم خارجية**: التطبيق يعمل بالكامل في المتصفح
- **أمان الكاميرا**: يطلب إذن صريح للوصول للكاميرا
- **HTTPS مطلوب**: للوصول للكاميرا في الإنتاج

## 🌐 متطلبات المتصفح

### ✅ **مدعوم بالكامل:**
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### ⚠️ **دعم جزئي:**
- Internet Explorer (غير مدعوم)
- متصفحات قديمة (قد تفتقر لبعض الميزات)

## 🛠️ التطوير

### البنية:
```
web-version/
├── index.html          # الواجهة الرئيسية
├── app.js             # منطق التطبيق
├── manifest.json      # إعدادات PWA
├── sw.js             # Service Worker
├── server.py         # خادم Python
└── README.md         # هذا الملف
```

### التبعيات:
- **jsQR**: مكتبة مسح رموز QR
- **qrcode.js**: مكتبة إنشاء رموز QR
- **Service Worker**: للعمل offline

## 🐛 استكشاف الأخطاء

### مشكلة الكاميرا:
- تأكد من منح إذن الكاميرا
- تأكد من أن الموقع يعمل على HTTPS أو localhost
- جرب متصفح آخر

### مشكلة المسح:
- تأكد من وضوح رمز QR
- تأكد من الإضاءة الجيدة
- جرب تبديل الكاميرا

### مشكلة التثبيت:
- تأكد من دعم المتصفح لـ PWA
- تأكد من وجود manifest.json
- تحقق من console للأخطاء

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من console المتصفح للأخطاء
2. تأكد من تحديث المتصفح
3. جرب في وضع incognito
4. تحقق من إعدادات الكاميرا

---

**🎉 استمتع باستخدام QR Scanner MAX في المتصفح! 🎉**
