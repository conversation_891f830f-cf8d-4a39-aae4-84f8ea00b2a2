<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/splashBody"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    tools:context=".activity.SplashActivity">

    <ImageView
        android:id="@+id/splashIcon"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:layout_centerInParent="true"
        android:scaleType="centerInside"
        android:src="@drawable/splash_icon"
        android:tint="@color/white" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/splashIcon"
        android:gravity="center"
        android:text="@string/splash_text"
        android:textColor="@color/white"
        android:textSize="20sp" />

</RelativeLayout>
