package com.bezruk.qrcodebarcode.activity;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.Window;
import android.view.WindowManager;
import android.widget.RelativeLayout;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.splashscreen.SplashScreen;

import com.bezruk.qrcodebarcode.R;
import com.bezruk.qrcodebarcode.utility.ActivityUtils;
import com.bezruk.qrcodebarcode.utility.PermissionHelper;

public class SplashActivity extends AppCompatActivity {

    private static final int SPLASH_DELAY = 2000; // 2 seconds
    private Handler splashHandler;
    private Runnable splashRunnable;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // Install splash screen for Android 12+
        SplashScreen splashScreen = SplashScreen.installSplashScreen(this);

        super.onCreate(savedInstanceState);

        // Keep splash screen visible longer
        splashScreen.setKeepOnScreenCondition(() -> true);

        supportRequestWindowFeature(Window.FEATURE_NO_TITLE);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);

        setContentView(R.layout.activity_splash);

        initSplashTimer();
    }

    private void initSplashTimer() {
        splashHandler = new Handler(Looper.getMainLooper());
        splashRunnable = new Runnable() {
            @Override
            public void run() {
                checkPermissionsAndProceed();
            }
        };

        splashHandler.postDelayed(splashRunnable, SPLASH_DELAY);
    }

    private void checkPermissionsAndProceed() {
        // Check if camera permission is granted
        if (PermissionHelper.isCameraPermissionGranted(this)) {
            navigateToMainActivity();
        } else {
            // Request camera permission
            PermissionHelper.requestCameraPermission(this, new PermissionHelper.PermissionCallback() {
                @Override
                public void onPermissionGranted() {
                    navigateToMainActivity();
                }

                @Override
                public void onPermissionDenied() {
                    // Still navigate to main activity, permission will be requested when needed
                    navigateToMainActivity();
                }
            });
        }
    }

    private void navigateToMainActivity() {
        Intent intent = new Intent(SplashActivity.this, MainActivity.class);
        startActivity(intent);
        finish();

        // Add smooth transition
        overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (splashHandler != null && splashRunnable != null) {
            splashHandler.removeCallbacks(splashRunnable);
        }
    }
}

