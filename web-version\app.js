// QR Scanner MAX - Web Version JavaScript

let video = document.getElementById('video');
let canvas = document.createElement('canvas');
let context = canvas.getContext('2d');
let scanning = false;
let currentStream = null;
let facingMode = 'environment'; // 'user' for front camera, 'environment' for back camera
let lastResult = '';

// Initialize the app
document.addEventListener('DOMContentLoaded', function() {
    showStatus('تطبيق QR Scanner MAX جاهز!');
    
    // Register service worker for PWA
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('sw.js')
            .then(registration => console.log('SW registered'))
            .catch(error => console.log('SW registration failed'));
    }
});

// Show status message
function showStatus(message, type = 'success') {
    const status = document.getElementById('status');
    status.textContent = message;
    status.style.display = 'block';
    status.style.background = type === 'error' ? '#f44336' : '#4caf50';
    
    setTimeout(() => {
        status.style.display = 'none';
    }, 3000);
}

// Start QR scanner
async function startScanner() {
    try {
        if (currentStream) {
            stopScanner();
        }
        
        const constraints = {
            video: {
                facingMode: facingMode,
                width: { ideal: 1280 },
                height: { ideal: 720 }
            }
        };
        
        currentStream = await navigator.mediaDevices.getUserMedia(constraints);
        video.srcObject = currentStream;
        
        video.onloadedmetadata = () => {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            scanning = true;
            scanQRCode();
            showStatus('بدء المسح... وجه الكاميرا نحو رمز QR');
        };
        
    } catch (error) {
        console.error('Error accessing camera:', error);
        showStatus('خطأ في الوصول للكاميرا: ' + error.message, 'error');
    }
}

// Stop QR scanner
function stopScanner() {
    scanning = false;
    
    if (currentStream) {
        currentStream.getTracks().forEach(track => track.stop());
        currentStream = null;
    }
    
    video.srcObject = null;
    showStatus('تم إيقاف المسح');
}

// Switch camera (front/back)
function switchCamera() {
    facingMode = facingMode === 'environment' ? 'user' : 'environment';
    if (scanning) {
        startScanner();
    }
    showStatus('تم تبديل الكاميرا');
}

// Scan QR code from video
function scanQRCode() {
    if (!scanning) return;
    
    if (video.readyState === video.HAVE_ENOUGH_DATA) {
        context.drawImage(video, 0, 0, canvas.width, canvas.height);
        const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
        
        const code = jsQR(imageData.data, imageData.width, imageData.height);
        
        if (code && code.data !== lastResult) {
            lastResult = code.data;
            displayResult(code.data);
            vibrate();
            playBeep();
        }
    }
    
    if (scanning) {
        requestAnimationFrame(scanQRCode);
    }
}

// Display scan result
function displayResult(data) {
    const resultDiv = document.getElementById('result');
    const resultText = document.getElementById('resultText');
    
    resultText.textContent = data;
    resultDiv.classList.add('show');
    
    showStatus('تم العثور على رمز QR!');
    
    // Auto-detect and handle different types
    if (isURL(data)) {
        resultText.innerHTML = `<a href="${data}" target="_blank" style="color: #2196F3; text-decoration: none;">${data}</a>`;
    } else if (isEmail(data)) {
        resultText.innerHTML = `<a href="mailto:${data}" style="color: #2196F3; text-decoration: none;">${data}</a>`;
    } else if (isPhone(data)) {
        resultText.innerHTML = `<a href="tel:${data}" style="color: #2196F3; text-decoration: none;">${data}</a>`;
    }
}

// Generate QR code
function generateQR() {
    const text = document.getElementById('textInput').value.trim();
    const canvas = document.getElementById('qrCanvas');
    
    if (!text) {
        showStatus('يرجى إدخال نص لإنشاء رمز QR', 'error');
        return;
    }
    
    QRCode.toCanvas(canvas, text, {
        width: 300,
        margin: 2,
        color: {
            dark: '#000000',
            light: '#FFFFFF'
        }
    }, function(error) {
        if (error) {
            showStatus('خطأ في إنشاء رمز QR: ' + error.message, 'error');
        } else {
            canvas.style.display = 'block';
            showStatus('تم إنشاء رمز QR بنجاح!');
        }
    });
}

// Download QR code
function downloadQR() {
    const canvas = document.getElementById('qrCanvas');
    const text = document.getElementById('textInput').value.trim();
    
    if (!canvas.style.display || canvas.style.display === 'none') {
        showStatus('يرجى إنشاء رمز QR أولاً', 'error');
        return;
    }
    
    const link = document.createElement('a');
    link.download = `qr-code-${Date.now()}.png`;
    link.href = canvas.toDataURL();
    link.click();
    
    showStatus('تم تحميل رمز QR');
}

// Copy result to clipboard
async function copyResult() {
    const resultText = document.getElementById('resultText').textContent;
    
    try {
        await navigator.clipboard.writeText(resultText);
        showStatus('تم نسخ النتيجة إلى الحافظة');
    } catch (error) {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = resultText;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showStatus('تم نسخ النتيجة');
    }
}

// Share result
async function shareResult() {
    const resultText = document.getElementById('resultText').textContent;
    
    if (navigator.share) {
        try {
            await navigator.share({
                title: 'QR Scanner MAX Result',
                text: resultText,
                url: isURL(resultText) ? resultText : undefined
            });
            showStatus('تم مشاركة النتيجة');
        } catch (error) {
            if (error.name !== 'AbortError') {
                fallbackShare(resultText);
            }
        }
    } else {
        fallbackShare(resultText);
    }
}

// Fallback share method
function fallbackShare(text) {
    const shareUrl = `https://wa.me/?text=${encodeURIComponent(text)}`;
    window.open(shareUrl, '_blank');
    showStatus('تم فتح WhatsApp للمشاركة');
}

// Utility functions
function isURL(str) {
    try {
        new URL(str);
        return true;
    } catch {
        return false;
    }
}

function isEmail(str) {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(str);
}

function isPhone(str) {
    return /^[\+]?[1-9][\d]{0,15}$/.test(str.replace(/[\s\-\(\)]/g, ''));
}

// Vibrate on scan
function vibrate() {
    if (navigator.vibrate) {
        navigator.vibrate(100);
    }
}

// Play beep sound
function playBeep() {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    oscillator.frequency.value = 800;
    oscillator.type = 'square';
    
    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.1);
}

// Keyboard shortcuts
document.addEventListener('keydown', function(event) {
    if (event.ctrlKey || event.metaKey) {
        switch(event.key) {
            case 's':
                event.preventDefault();
                if (scanning) {
                    stopScanner();
                } else {
                    startScanner();
                }
                break;
            case 'g':
                event.preventDefault();
                generateQR();
                break;
            case 'c':
                event.preventDefault();
                copyResult();
                break;
        }
    }
});

// Handle Enter key in text input
document.getElementById('textInput').addEventListener('keypress', function(event) {
    if (event.key === 'Enter') {
        generateQR();
    }
});
