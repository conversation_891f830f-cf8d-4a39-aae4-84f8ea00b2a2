<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:id="@+id/content_frame"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    </FrameLayout>

    <LinearLayout
        android:id="@+id/top_btn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:gravity="top|center">


        <ImageView
            android:id="@+id/flash"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:layout_margin="@dimen/fab_margin"
            android:background="@color/fab"
            android:padding="8dp"
            android:src="@drawable/ic_flash_on" />

        <ImageView
            android:id="@+id/gallery"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:layout_margin="@dimen/fab_margin"
            android:background="@color/fab"
            android:padding="7dp"
            android:src="@drawable/ic_gallery_scan" />

        <ImageView
            android:id="@+id/camera"
            android:layout_width="45dp"
            android:layout_height="45dp"
            android:layout_margin="@dimen/fab_margin"
            android:background="@color/fab"
            android:padding="7dp"
            android:src="@drawable/ic_switch_cam" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_below="@id/top_btn"
        android:layout_alignParentStart="false"
        android:layout_alignParentLeft="false"
        android:layout_centerInParent="true"
        android:gravity="center">


        <Button
            android:id="@+id/allowCameraUseBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:clickable="true"
            android:text="@string/allowCameraUse"
            android:textSize="18sp"
            android:visibility="gone" />

    </LinearLayout>


</RelativeLayout>
