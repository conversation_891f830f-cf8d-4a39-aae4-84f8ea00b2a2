package com.pes.androidmaterialcolorpickerdialog;

import android.app.AlertDialog;
import android.content.Context;
import android.graphics.Color;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.GridView;
import android.widget.ImageView;

/**
 * Simple replacement for MaterialColorPicker
 */
public class ColorPicker {
    
    private Context context;
    private int selectedColor = Color.BLACK;
    private ColorPickerCallback callback;
    
    // Predefined colors
    private static final int[] COLORS = {
        Color.BLACK, Color.WHITE, Color.RED, Color.GREEN, 
        Color.BLUE, Color.YELLOW, Color.CYAN, Color.MAGENTA,
        Color.GRAY, Color.DKGRAY, 0xFF800080, 0xFFFFA500,
        0xFF8B4513, 0xFFFF1493, 0xFF00FF7F, 0xFF4169E1
    };
    
    public ColorPicker(Context context, int r, int g, int b) {
        this.context = context;
        this.selectedColor = Color.rgb(r, g, b);
    }
    
    public void setCallback(ColorPickerCallback callback) {
        this.callback = callback;
    }
    
    public void show() {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        builder.setTitle("Select Color");
        
        GridView gridView = new GridView(context);
        gridView.setNumColumns(4);
        gridView.setAdapter(new ColorAdapter());
        
        gridView.setOnItemClickListener((parent, view, position, id) -> {
            selectedColor = COLORS[position];
            if (callback != null) {
                callback.onColorSelected(selectedColor);
            }
        });
        
        builder.setView(gridView);
        builder.setNegativeButton("Cancel", null);
        builder.show();
    }
    
    public View findViewById(int id) {
        // Dummy implementation for compatibility
        return new View(context);
    }

    public int getColor() {
        return selectedColor;
    }

    public void dismiss() {
        // Dummy implementation for compatibility
    }
    
    private class ColorAdapter extends BaseAdapter {
        
        @Override
        public int getCount() {
            return COLORS.length;
        }
        
        @Override
        public Object getItem(int position) {
            return COLORS[position];
        }
        
        @Override
        public long getItemId(int position) {
            return position;
        }
        
        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            ImageView imageView;
            if (convertView == null) {
                imageView = new ImageView(context);
                int size = 100;
                imageView.setLayoutParams(new GridView.LayoutParams(size, size));
                imageView.setPadding(8, 8, 8, 8);
            } else {
                imageView = (ImageView) convertView;
            }
            
            imageView.setBackgroundColor(COLORS[position]);
            return imageView;
        }
    }
}
