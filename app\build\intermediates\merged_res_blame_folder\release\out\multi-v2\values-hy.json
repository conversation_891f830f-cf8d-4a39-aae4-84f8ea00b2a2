{"logs": [{"outputFile": "com.bezruk.qrcodebarcode.app-mergeReleaseResources-41:/values-hy/values-hy.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\034e53a5369349e96a448cde5bcf995e\\transformed\\jetified-play-services-basement-18.2.0\\res\\values-hy\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "146", "endOffsets": "341"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "4785", "endColumns": "150", "endOffsets": "4931"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ede285f672e73ef59c1f7bc9b2e633f9\\transformed\\jetified-play-services-base-18.0.0\\res\\values-hy\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,456,586,690,840,972,1095,1204,1367,1471,1635,1767,1925,2087,2148,2211", "endColumns": "101,160,129,103,149,131,122,108,162,103,163,131,157,161,60,62,77", "endOffsets": "294,455,585,689,839,971,1094,1203,1366,1470,1634,1766,1924,2086,2147,2210,2288"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3742,3848,4013,4147,4255,4409,4545,4672,4936,5103,5211,5379,5515,5677,5843,5908,5975", "endColumns": "105,164,133,107,153,135,126,112,166,107,167,135,161,165,64,66,81", "endOffsets": "3843,4008,4142,4250,4404,4540,4667,4780,5098,5206,5374,5510,5672,5838,5903,5970,6052"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1269e761ba2b2764df14d796e474c793\\transformed\\appcompat-1.6.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1917,2023,2129,2228,2338,2446,2547,2717,2814", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "208,308,418,507,613,730,812,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1912,2018,2124,2223,2333,2441,2542,2712,2809,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,143", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,423,523,633,722,828,945,1027,1107,1198,1291,1386,1480,1580,1673,1768,1862,1953,2044,2127,2233,2339,2438,2548,2656,2757,2927,12774", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "418,518,628,717,823,940,1022,1102,1193,1286,1381,1475,1575,1668,1763,1857,1948,2039,2122,2228,2334,2433,2543,2651,2752,2922,3019,12852"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6a153e0be5a372f172b5b708d9293a44\\transformed\\material-1.10.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,341,417,497,589,677,772,902,983,1047,1144,1229,1291,1378,1440,1504,1565,1632,1693,1747,1869,1926,1986,2040,2121,2256,2340,2425,2561,2636,2711,2854,2949,3029,3085,3138,3204,3278,3357,3443,3526,3597,3673,3749,3826,3932,4020,4100,4196,4292,4366,4444,4544,4595,4679,4748,4835,4926,4988,5052,5115,5186,5291,5397,5497,5600,5660,5717", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,75,75,79,91,87,94,129,80,63,96,84,61,86,61,63,60,66,60,53,121,56,59,53,80,134,83,84,135,74,74,142,94,79,55,52,65,73,78,85,82,70,75,75,76,105,87,79,95,95,73,77,99,50,83,68,86,90,61,63,62,70,104,105,99,102,59,56,84", "endOffsets": "260,336,412,492,584,672,767,897,978,1042,1139,1224,1286,1373,1435,1499,1560,1627,1688,1742,1864,1921,1981,2035,2116,2251,2335,2420,2556,2631,2706,2849,2944,3024,3080,3133,3199,3273,3352,3438,3521,3592,3668,3744,3821,3927,4015,4095,4191,4287,4361,4439,4539,4590,4674,4743,4830,4921,4983,5047,5110,5181,5286,5392,5492,5595,5655,5712,5797"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,60,61,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3024,3100,3176,3256,3348,3436,3531,3661,6161,6225,6639,6724,6786,6873,6935,6999,7060,7127,7188,7242,7364,7421,7481,7535,7616,7751,7835,7920,8056,8131,8206,8349,8444,8524,8580,8633,8699,8773,8852,8938,9021,9092,9168,9244,9321,9427,9515,9595,9691,9787,9861,9939,10039,10090,10174,10243,10330,10421,10483,10547,10610,10681,10786,10892,10992,11095,11155,12293", "endLines": "5,33,34,35,36,37,38,39,40,60,61,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,135", "endColumns": "12,75,75,79,91,87,94,129,80,63,96,84,61,86,61,63,60,66,60,53,121,56,59,53,80,134,83,84,135,74,74,142,94,79,55,52,65,73,78,85,82,70,75,75,76,105,87,79,95,95,73,77,99,50,83,68,86,90,61,63,62,70,104,105,99,102,59,56,84", "endOffsets": "310,3095,3171,3251,3343,3431,3526,3656,3737,6220,6317,6719,6781,6868,6930,6994,7055,7122,7183,7237,7359,7416,7476,7530,7611,7746,7830,7915,8051,8126,8201,8344,8439,8519,8575,8628,8694,8768,8847,8933,9016,9087,9163,9239,9316,9422,9510,9590,9686,9782,9856,9934,10034,10085,10169,10238,10325,10416,10478,10542,10605,10676,10781,10887,10987,11090,11150,11207,12373"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b544b6c935c841456a5135f6852c436f\\transformed\\jetified-play-services-ads-22.6.0\\res\\values-hy\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,250,299,352,420,489,597,684,788,840,968,1036,1134,1228,1269,1347,1383,1417,1473,1551,1596", "endColumns": "50,48,52,67,68,107,86,103,51,127,67,97,93,40,77,35,33,55,77,44,55", "endOffsets": "249,298,351,419,488,596,683,787,839,967,1035,1133,1227,1268,1346,1382,1416,1472,1550,1595,1651"}, "to": {"startLines": "122,123,124,125,126,127,128,129,130,131,132,133,134,136,137,138,139,140,141,142,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11212,11267,11320,11377,11449,11522,11634,11725,11833,11889,12021,12093,12195,12378,12423,12505,12545,12583,12643,12725,12958", "endColumns": "54,52,56,71,72,111,90,107,55,131,71,101,97,44,81,39,37,59,81,48,59", "endOffsets": "11262,11315,11372,11444,11517,11629,11720,11828,11884,12016,12088,12190,12288,12418,12500,12540,12578,12638,12720,12769,13013"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\49ae36518fcf1866e9d9d51daa2bfc7e\\transformed\\browser-1.7.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,262,373", "endColumns": "103,102,110,102", "endOffsets": "154,257,368,471"}, "to": {"startLines": "59,62,63,64", "startColumns": "4,4,4,4", "startOffsets": "6057,6322,6425,6536", "endColumns": "103,102,110,102", "endOffsets": "6156,6420,6531,6634"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\75919e89ab9acfe92b320b18b5eba894\\transformed\\core-1.9.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "144", "startColumns": "4", "startOffsets": "12857", "endColumns": "100", "endOffsets": "12953"}}]}]}