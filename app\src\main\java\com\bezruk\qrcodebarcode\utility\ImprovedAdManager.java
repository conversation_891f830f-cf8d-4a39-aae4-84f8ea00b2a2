package com.bezruk.qrcodebarcode.utility;

import android.app.Activity;
import android.content.Context;
import android.util.Log;

import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.AdListener;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.FullScreenContentCallback;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.MobileAds;
import com.google.android.gms.ads.initialization.InitializationStatus;
import com.google.android.gms.ads.initialization.OnInitializationCompleteListener;
import com.google.android.gms.ads.interstitial.InterstitialAd;
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback;

/**
 * Improved Ad Manager with better error handling and performance
 */
public class ImprovedAdManager {
    
    private static final String TAG = "ImprovedAdManager";
    private static ImprovedAdManager instance;
    private Context context;
    private InterstitialAd interstitialAd;
    private boolean isInitialized = false;
    
    // Test ad unit IDs - replace with your actual ad unit IDs
    private static final String BANNER_AD_UNIT_ID = "ca-app-pub-3940256099942544/6300978111";
    private static final String INTERSTITIAL_AD_UNIT_ID = "ca-app-pub-3940256099942544/1033173712";
    
    private ImprovedAdManager(Context context) {
        this.context = context.getApplicationContext();
        initializeAds();
    }
    
    public static synchronized ImprovedAdManager getInstance(Context context) {
        if (instance == null) {
            instance = new ImprovedAdManager(context);
        }
        return instance;
    }
    
    /**
     * Initialize Google Mobile Ads SDK
     */
    private void initializeAds() {
        MobileAds.initialize(context, new OnInitializationCompleteListener() {
            @Override
            public void onInitializationComplete(InitializationStatus initializationStatus) {
                isInitialized = true;
                Log.d(TAG, "AdMob initialized successfully");
                loadInterstitialAd();
            }
        });
    }
    
    /**
     * Load banner ad with improved error handling
     */
    public void loadBannerAd(AdView adView) {
        if (!isInitialized || adView == null) {
            Log.w(TAG, "AdMob not initialized or AdView is null");
            return;
        }
        
        AdRequest adRequest = new AdRequest.Builder().build();
        
        adView.setAdListener(new AdListener() {
            @Override
            public void onAdClicked() {
                Log.d(TAG, "Banner ad clicked");
            }
            
            @Override
            public void onAdClosed() {
                Log.d(TAG, "Banner ad closed");
            }
            
            @Override
            public void onAdFailedToLoad(LoadAdError adError) {
                Log.e(TAG, "Banner ad failed to load: " + adError.getMessage());
            }
            
            @Override
            public void onAdImpression() {
                Log.d(TAG, "Banner ad impression recorded");
            }
            
            @Override
            public void onAdLoaded() {
                Log.d(TAG, "Banner ad loaded successfully");
            }
            
            @Override
            public void onAdOpened() {
                Log.d(TAG, "Banner ad opened");
            }
        });
        
        adView.loadAd(adRequest);
    }
    
    /**
     * Load interstitial ad
     */
    private void loadInterstitialAd() {
        if (!isInitialized) {
            Log.w(TAG, "AdMob not initialized");
            return;
        }
        
        AdRequest adRequest = new AdRequest.Builder().build();
        
        InterstitialAd.load(context, INTERSTITIAL_AD_UNIT_ID, adRequest,
            new InterstitialAdLoadCallback() {
                @Override
                public void onAdLoaded(InterstitialAd ad) {
                    interstitialAd = ad;
                    Log.d(TAG, "Interstitial ad loaded successfully");
                    
                    interstitialAd.setFullScreenContentCallback(new FullScreenContentCallback() {
                        @Override
                        public void onAdClicked() {
                            Log.d(TAG, "Interstitial ad clicked");
                        }
                        
                        @Override
                        public void onAdDismissedFullScreenContent() {
                            Log.d(TAG, "Interstitial ad dismissed");
                            interstitialAd = null;
                            loadInterstitialAd(); // Load next ad
                        }
                        
                        @Override
                        public void onAdFailedToShowFullScreenContent(AdError adError) {
                            Log.e(TAG, "Interstitial ad failed to show: " + adError.getMessage());
                            interstitialAd = null;
                        }
                        
                        @Override
                        public void onAdImpression() {
                            Log.d(TAG, "Interstitial ad impression recorded");
                        }
                        
                        @Override
                        public void onAdShowedFullScreenContent() {
                            Log.d(TAG, "Interstitial ad showed full screen content");
                        }
                    });
                }
                
                @Override
                public void onAdFailedToLoad(LoadAdError loadAdError) {
                    Log.e(TAG, "Interstitial ad failed to load: " + loadAdError.getMessage());
                    interstitialAd = null;
                }
            });
    }
    
    /**
     * Show interstitial ad if available
     */
    public void showInterstitialAd(Activity activity) {
        if (interstitialAd != null && activity != null) {
            interstitialAd.show(activity);
        } else {
            Log.w(TAG, "Interstitial ad not ready or activity is null");
            loadInterstitialAd(); // Try to load ad for next time
        }
    }
    
    /**
     * Check if interstitial ad is ready
     */
    public boolean isInterstitialAdReady() {
        return interstitialAd != null;
    }
    
    /**
     * Pause ad requests (call in onPause)
     */
    public void pauseAds(AdView adView) {
        if (adView != null) {
            adView.pause();
        }
    }
    
    /**
     * Resume ad requests (call in onResume)
     */
    public void resumeAds(AdView adView) {
        if (adView != null) {
            adView.resume();
        }
    }
    
    /**
     * Destroy ads (call in onDestroy)
     */
    public void destroyAds(AdView adView) {
        if (adView != null) {
            adView.destroy();
        }
    }
    
    /**
     * Get banner ad unit ID
     */
    public String getBannerAdUnitId() {
        return BANNER_AD_UNIT_ID;
    }
    
    /**
     * Check if ads are initialized
     */
    public boolean isInitialized() {
        return isInitialized;
    }
}
