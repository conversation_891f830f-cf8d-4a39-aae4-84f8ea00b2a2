<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/Top_btns"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/scanned_res_btn"
            android:layout_width="match_parent"
            android:layout_height="42dp"
            android:layout_marginLeft="6dp"
            android:layout_marginRight="3dp"
            android:layout_weight="1"
            android:background="@color/colorPrimary"
            android:gravity="center"
            android:text="Scanned history"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/created_res_btn"
            android:layout_width="match_parent"
            android:layout_height="42dp"
            android:layout_marginLeft="3dp"
            android:layout_marginTop="6dp"
            android:layout_marginRight="6dp"
            android:layout_marginBottom="6dp"
            android:layout_weight="1"
            android:background="@color/grey"
            android:gravity="center"
            android:text="Created history"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

    </LinearLayout>

    <ImageView
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="3dp"
        android:layout_below="@+id/Top_btns"
        android:layout_alignParentStart="true"
        android:layout_marginLeft="6dp"
        android:layout_marginRight="6dp"
        android:background="#727272" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/line"
        android:layout_alignParentStart="true"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="6dp"
        android:layout_marginTop="6dp"
        android:layout_marginRight="6dp" />

    <TextView
        android:id="@+id/noResultView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:text="@string/no_history"
        android:textSize="18sp" />

    <ImageButton
        android:id="@+id/deleteAll"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="@dimen/fab_margin_16"
        android:layout_marginTop="@dimen/fab_margin_16"
        android:layout_marginEnd="@dimen/fab_margin_16"
        android:layout_marginBottom="@dimen/fab_margin_16"
        android:background="@color/colorPrimary"
        android:padding="7dp"
        android:elevation="5dp"
        android:scaleType="fitXY"
        android:src="@drawable/ic_delete"
        android:tint="@color/white" />

</RelativeLayout>