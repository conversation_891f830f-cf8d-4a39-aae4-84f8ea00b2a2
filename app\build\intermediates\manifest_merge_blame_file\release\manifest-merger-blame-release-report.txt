1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.bezruk.qrcodebarcode"
4    android:versionCode="7"
5    android:versionName="1.0.6" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="33" />
10
11    <!-- Camera permission for QR/Barcode scanning -->
12    <uses-permission android:name="android.permission.CAMERA" />
12-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:6:5-65
12-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:6:22-62
13
14    <!-- Storage permissions with proper scoped storage handling -->
15    <uses-permission
15-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:9:5-10:38
16        android:name="android.permission.READ_EXTERNAL_STORAGE"
16-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:9:22-77
17        android:maxSdkVersion="32" />
17-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:10:9-35
18    <uses-permission
18-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:11:5-13:40
19        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
19-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:11:22-78
20        android:maxSdkVersion="29" />
20-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:12:9-35
21    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
21-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:14:5-76
21-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:14:22-73
22
23    <!-- Vibration for feedback -->
24    <uses-permission android:name="android.permission.VIBRATE" />
24-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:17:5-66
24-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:17:22-63
25
26    <!-- Phone permission for calling -->
27    <uses-permission android:name="android.permission.CALL_PHONE" />
27-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:20:5-69
27-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:20:22-66
28
29    <!-- Contacts permission -->
30    <uses-permission android:name="android.permission.READ_CONTACTS" />
30-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:23:5-72
30-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:23:22-69
31
32    <!-- WiFi permissions -->
33    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
33-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:26:5-76
33-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:26:22-73
34    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
34-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:27:5-76
34-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:27:22-73
35
36    <!-- Internet for ads -->
37    <uses-permission android:name="android.permission.INTERNET" />
37-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:30:5-67
37-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:30:22-64
38    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
38-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:31:5-79
38-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:31:22-76
39
40    <!-- Billing permission -->
41    <uses-permission android:name="com.android.vending.BILLING" />
41-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:34:5-67
41-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:34:22-64
42
43    <!-- Camera features -->
44    <uses-feature
44-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:37:5-39:35
45        android:name="android.hardware.camera"
45-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:38:9-47
46        android:required="true" />
46-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:39:9-32
47    <uses-feature
47-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:40:5-42:36
48        android:name="android.hardware.camera.autofocus"
48-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:41:9-57
49        android:required="false" />
49-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:42:9-33
50    <uses-feature
50-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:43:5-45:36
51        android:name="android.hardware.camera.flash"
51-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:44:9-53
52        android:required="false" />
52-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:45:9-33
53    <uses-feature
53-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
54        android:name="android.hardware.camera.front"
54-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
55        android:required="false" />
55-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
56    <uses-feature
56-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
57        android:name="android.hardware.screen.landscape"
57-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
58        android:required="false" />
58-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
59    <uses-feature
59-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
60        android:name="android.hardware.wifi"
60-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
61        android:required="false" />
61-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
62
63    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
63-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:26:5-79
63-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:26:22-76
64    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
64-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:27:5-82
64-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:27:22-79
65    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
65-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:28:5-88
65-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:28:22-85
66    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Android package visibility setting -->
66-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:29:5-83
66-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:29:22-80
67    <queries>
67-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:35:5-51:15
68
69        <!-- For browser content -->
70        <intent>
70-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:38:9-44:18
71            <action android:name="android.intent.action.VIEW" />
71-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:39:13-65
71-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:39:21-62
72
73            <category android:name="android.intent.category.BROWSABLE" />
73-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:41:13-74
73-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:41:23-71
74
75            <data android:scheme="https" />
75-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:43:13-44
75-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:43:19-41
76        </intent>
77        <!-- End of browser content -->
78        <!-- For CustomTabsService -->
79        <intent>
79-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:47:9-49:18
80            <action android:name="android.support.customtabs.action.CustomTabsService" />
80-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:48:13-90
80-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:48:21-87
81        </intent>
82        <!-- End of CustomTabsService -->
83    </queries>
84
85    <uses-permission android:name="android.permission.WAKE_LOCK" />
85-->[com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\841e3cab8a4af71928abf9f5a0c9ceda\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:5-68
85-->[com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\841e3cab8a4af71928abf9f5a0c9ceda\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:22-65
86    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
86-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
86-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
87
88    <permission
88-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
89        android:name="com.bezruk.qrcodebarcode.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
89-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
90        android:protectionLevel="signature" />
90-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
91
92    <uses-permission android:name="com.bezruk.qrcodebarcode.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
92-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
92-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
93
94    <application
94-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:47:5-101:19
95        android:allowBackup="true"
95-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:48:9-35
96        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
96-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
97        android:dataExtractionRules="@xml/data_extraction_rules"
97-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:49:9-65
98        android:extractNativeLibs="true"
99        android:fullBackupContent="@xml/backup_rules"
99-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:50:9-54
100        android:hardwareAccelerated="true"
100-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:55:9-43
101        android:icon="@drawable/ic_launcher"
101-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:51:9-45
102        android:label="@string/app_name"
102-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:52:9-41
103        android:largeHeap="true"
103-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:56:9-33
104        android:requestLegacyExternalStorage="true"
104-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:57:9-52
105        android:supportsRtl="true"
105-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:53:9-35
106        android:theme="@style/AppTheme" >
106-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:54:9-40
107        <activity
107-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:60:9-70:20
108            android:name="com.bezruk.qrcodebarcode.activity.SplashActivity"
108-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:61:13-52
109            android:exported="true"
109-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:65:13-36
110            android:label="@string/app_name"
110-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:62:13-45
111            android:screenOrientation="portrait"
111-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:64:13-49
112            android:theme="@style/AppTheme.NoActionBar" >
112-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:63:13-56
113            <intent-filter>
113-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:66:13-69:29
114                <action android:name="android.intent.action.MAIN" />
114-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:67:17-69
114-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:67:25-66
115
116                <category android:name="android.intent.category.LAUNCHER" />
116-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:68:17-77
116-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:68:27-74
117            </intent-filter>
118        </activity>
119        <activity
119-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:72:9-77:20
120            android:name="com.bezruk.qrcodebarcode.activity.MainActivity"
120-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:73:13-50
121            android:exported="false"
121-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:76:13-37
122            android:screenOrientation="portrait"
122-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:74:13-49
123            android:theme="@style/AppTheme.NoActionBar" >
123-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:75:13-56
124        </activity>
125        <activity
125-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:79:9-83:39
126            android:name="com.bezruk.qrcodebarcode.activity.ResultActivity"
126-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:80:13-52
127            android:exported="false"
127-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:83:13-37
128            android:screenOrientation="portrait"
128-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:81:13-49
129            android:theme="@style/AppTheme.NoActionBar" />
129-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:82:13-56
130
131        <!-- Google AdMob App ID -->
132        <meta-data
132-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:86:9-88:69
133            android:name="com.google.android.gms.ads.APPLICATION_ID"
133-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:87:13-69
134            android:value="ca-app-pub-3940256099942544~**********" />
134-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:88:13-67
135
136        <!-- FileProvider for sharing files -->
137        <provider
138            android:name="androidx.core.content.FileProvider"
138-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:92:13-62
139            android:authorities="com.bezruk.qrcodebarcode.fileprovider"
139-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:93:13-64
140            android:exported="false"
140-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:95:13-37
141            android:grantUriPermissions="true" >
141-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:94:13-47
142            <meta-data
142-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:96:13-98:54
143                android:name="android.support.FILE_PROVIDER_PATHS"
143-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:97:17-67
144                android:resource="@xml/file_paths" />
144-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:98:17-51
145        </provider>
146
147        <activity
147-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
148            android:name="com.journeyapps.barcodescanner.CaptureActivity"
148-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
149            android:clearTaskOnLaunch="true"
149-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
150            android:screenOrientation="sensorLandscape"
150-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
151            android:stateNotNeeded="true"
151-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
152            android:theme="@style/zxing_CaptureTheme"
152-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
153            android:windowSoftInputMode="stateAlwaysHidden" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
153-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
154        <activity
154-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:56:9-61:43
155            android:name="com.google.android.gms.ads.AdActivity"
155-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:57:13-65
156            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
156-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:58:13-122
157            android:exported="false"
157-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:59:13-37
158            android:theme="@android:style/Theme.Translucent" />
158-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:60:13-61
159
160        <provider
160-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:63:9-68:43
161            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
161-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:64:13-76
162            android:authorities="com.bezruk.qrcodebarcode.mobileadsinitprovider"
162-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:65:13-73
163            android:exported="false"
163-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:66:13-37
164            android:initOrder="100" />
164-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:67:13-36
165
166        <service
166-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:70:9-74:43
167            android:name="com.google.android.gms.ads.AdService"
167-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:71:13-64
168            android:enabled="true"
168-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:72:13-35
169            android:exported="false" />
169-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:73:13-37
170
171        <activity
171-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:76:9-80:43
172            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
172-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:77:13-82
173            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
173-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:78:13-122
174            android:exported="false" />
174-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:79:13-37
175        <activity
175-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:81:9-88:43
176            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
176-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:82:13-82
177            android:excludeFromRecents="true"
177-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:83:13-46
178            android:exported="false"
178-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:84:13-37
179            android:launchMode="singleTask"
179-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:85:13-44
180            android:taskAffinity=""
180-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:86:13-36
181            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
181-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:87:13-72
182
183        <property
183-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:90:9-92:62
184            android:name="android.adservices.AD_SERVICES_CONFIG"
184-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:91:13-65
185            android:resource="@xml/gma_ad_services_config" />
185-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:92:13-59
186
187        <activity
187-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ede285f672e73ef59c1f7bc9b2e633f9\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:20:9-22:45
188            android:name="com.google.android.gms.common.api.GoogleApiActivity"
188-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ede285f672e73ef59c1f7bc9b2e633f9\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:20:19-85
189            android:exported="false"
189-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ede285f672e73ef59c1f7bc9b2e633f9\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:22:19-43
190            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
190-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ede285f672e73ef59c1f7bc9b2e633f9\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:21:19-78
191
192        <meta-data
192-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\034e53a5369349e96a448cde5bcf995e\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
193            android:name="com.google.android.gms.version"
193-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\034e53a5369349e96a448cde5bcf995e\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
194            android:value="@integer/google_play_services_version" />
194-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\034e53a5369349e96a448cde5bcf995e\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
195
196        <provider
196-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4381fccdde393d19d04161b40313d198\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
197            android:name="androidx.startup.InitializationProvider"
197-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4381fccdde393d19d04161b40313d198\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
198            android:authorities="com.bezruk.qrcodebarcode.androidx-startup"
198-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4381fccdde393d19d04161b40313d198\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
199            android:exported="false" >
199-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4381fccdde393d19d04161b40313d198\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
200            <meta-data
200-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4381fccdde393d19d04161b40313d198\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
201                android:name="androidx.emoji2.text.EmojiCompatInitializer"
201-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4381fccdde393d19d04161b40313d198\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
202                android:value="androidx.startup" />
202-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4381fccdde393d19d04161b40313d198\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
203            <meta-data
203-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
204                android:name="androidx.work.WorkManagerInitializer"
204-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
205                android:value="androidx.startup" />
205-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
206            <meta-data
206-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\818eda85d3b5ca5901b62e2194b4fd5f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
207                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
207-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\818eda85d3b5ca5901b62e2194b4fd5f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
208                android:value="androidx.startup" />
208-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\818eda85d3b5ca5901b62e2194b4fd5f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
209            <meta-data
209-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
210                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
210-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
211                android:value="androidx.startup" />
211-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
212        </provider>
213
214        <uses-library
214-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\63caa8bf6109ecbe6af1511e60399f08\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
215            android:name="android.ext.adservices"
215-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\63caa8bf6109ecbe6af1511e60399f08\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
216            android:required="false" />
216-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\63caa8bf6109ecbe6af1511e60399f08\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
217
218        <service
218-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
219            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
219-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
220            android:directBootAware="false"
220-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
221            android:enabled="@bool/enable_system_alarm_service_default"
221-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
222            android:exported="false" />
222-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
223        <service
223-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
224            android:name="androidx.work.impl.background.systemjob.SystemJobService"
224-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
225            android:directBootAware="false"
225-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
226            android:enabled="@bool/enable_system_job_service_default"
226-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
227            android:exported="true"
227-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
228            android:permission="android.permission.BIND_JOB_SERVICE" />
228-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
229        <service
229-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
230            android:name="androidx.work.impl.foreground.SystemForegroundService"
230-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
231            android:directBootAware="false"
231-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
232            android:enabled="@bool/enable_system_foreground_service_default"
232-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
233            android:exported="false" />
233-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
234
235        <receiver
235-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
236            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
236-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
237            android:directBootAware="false"
237-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
238            android:enabled="true"
238-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
239            android:exported="false" />
239-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
240        <receiver
240-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
241            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
241-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
242            android:directBootAware="false"
242-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
243            android:enabled="false"
243-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
244            android:exported="false" >
244-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
245            <intent-filter>
245-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
246                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
246-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
246-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
247                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
247-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
247-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
248            </intent-filter>
249        </receiver>
250        <receiver
250-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
251            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
251-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
252            android:directBootAware="false"
252-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
253            android:enabled="false"
253-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
254            android:exported="false" >
254-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
255            <intent-filter>
255-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
256                <action android:name="android.intent.action.BATTERY_OKAY" />
256-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
256-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
257                <action android:name="android.intent.action.BATTERY_LOW" />
257-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
257-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
258            </intent-filter>
259        </receiver>
260        <receiver
260-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
261            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
261-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
262            android:directBootAware="false"
262-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
263            android:enabled="false"
263-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
264            android:exported="false" >
264-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
265            <intent-filter>
265-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
266                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
266-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
266-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
267                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
267-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
267-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
268            </intent-filter>
269        </receiver>
270        <receiver
270-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
271            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
271-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
272            android:directBootAware="false"
272-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
273            android:enabled="false"
273-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
274            android:exported="false" >
274-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
275            <intent-filter>
275-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
276                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
276-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
276-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
277            </intent-filter>
278        </receiver>
279        <receiver
279-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
280            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
280-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
281            android:directBootAware="false"
281-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
282            android:enabled="false"
282-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
283            android:exported="false" >
283-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
284            <intent-filter>
284-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
285                <action android:name="android.intent.action.BOOT_COMPLETED" />
285-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
285-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
286                <action android:name="android.intent.action.TIME_SET" />
286-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
286-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
287                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
287-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
287-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
288            </intent-filter>
289        </receiver>
290        <receiver
290-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
291            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
291-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
292            android:directBootAware="false"
292-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
293            android:enabled="@bool/enable_system_alarm_service_default"
293-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
294            android:exported="false" >
294-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
295            <intent-filter>
295-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
296                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
296-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
296-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
297            </intent-filter>
298        </receiver>
299        <receiver
299-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
300            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
300-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
301            android:directBootAware="false"
301-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
302            android:enabled="true"
302-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
303            android:exported="true"
303-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
304            android:permission="android.permission.DUMP" >
304-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
305            <intent-filter>
305-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
306                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
306-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
306-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
307            </intent-filter>
308        </receiver>
309        <receiver
309-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
310            android:name="androidx.profileinstaller.ProfileInstallReceiver"
310-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
311            android:directBootAware="false"
311-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
312            android:enabled="true"
312-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
313            android:exported="true"
313-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
314            android:permission="android.permission.DUMP" >
314-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
315            <intent-filter>
315-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
316                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
316-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
316-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
317            </intent-filter>
318            <intent-filter>
318-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
319                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
319-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
319-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
320            </intent-filter>
321            <intent-filter>
321-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
322                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
322-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
322-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
323            </intent-filter>
324            <intent-filter>
324-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
325                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
325-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
325-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
326            </intent-filter>
327        </receiver>
328
329        <service
329-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\43e211c18de3038f20610673a4c19103\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
330            android:name="androidx.room.MultiInstanceInvalidationService"
330-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\43e211c18de3038f20610673a4c19103\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
331            android:directBootAware="true"
331-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\43e211c18de3038f20610673a4c19103\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
332            android:exported="false" />
332-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\43e211c18de3038f20610673a4c19103\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
333    </application>
334
335</manifest>
