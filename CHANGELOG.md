# Changelog

All notable changes to QR Scanner MAX will be documented in this file.

## [1.0.7] - 2024-12-19

### 🚀 Added
- Modern splash screen support for Android 12+
- Enhanced permission management with <PERSON> library
- Improved file handling with scoped storage support
- New FileHelper utility for modern storage operations
- Enhanced AdManager with better error handling
- View binding support for better performance
- Comprehensive ProGuard rules for better optimization
- Modern backup and data extraction rules
- Support for Android 14 (API 34)

### 🔧 Changed
- Updated Android Gradle Plugin to 8.2.2
- Updated Gradle wrapper to 8.5
- Updated target SDK to 34 (Android 14)
- Updated compile SDK to 34
- Migrated to modern plugin syntax
- Enhanced build configuration with performance optimizations
- Updated all dependencies to latest stable versions
- Improved AndroidManifest.xml with modern security settings
- Enhanced gradle.properties with performance optimizations

### 🛡️ Security
- Added proper FileProvider configuration
- Enhanced permission handling for Android 13+
- Improved data protection with backup rules
- Added scoped storage support
- Enhanced file sharing security
- Updated permission declarations for modern Android

### 🐛 Fixed
- Fixed compatibility issues with Android 13+
- Resolved storage permission issues
- Fixed file sharing on modern Android versions
- Improved memory management
- Enhanced error handling throughout the app
- Fixed potential memory leaks

### 📦 Dependencies Updated
- androidx.appcompat: 1.6.1
- androidx.core: 1.12.0
- androidx.core:core-splashscreen: 1.0.1
- androidx.browser: 1.8.0
- androidx.constraintlayout: 2.1.4
- androidx.recyclerview: 1.3.2
- com.google.android.material: 1.11.0
- com.google.zxing:core: 3.5.2
- com.karumi:dexter: 6.2.3
- com.github.bumptech.glide:glide: 4.16.0

### 🔧 Technical Improvements
- Enabled R8 full mode for better optimization
- Added resource shrinking for smaller APK size
- Improved build performance with parallel builds
- Enhanced ProGuard configuration
- Added comprehensive testing framework
- Improved code organization and structure

### 📱 User Experience
- Faster app startup time
- Improved permission request flow
- Better error messages and handling
- Enhanced splash screen experience
- Smoother transitions and animations
- Better memory usage and performance

## [1.0.6] - Previous Version

### Features
- Basic QR code and barcode scanning
- Code generation functionality
- History management
- Gallery import support
- Flash and auto-focus support
- Color customization for generated codes
- Share and save functionality
- In-app advertising integration

### Technical Details
- Target SDK: 33 (Android 13)
- Minimum SDK: 21 (Android 5.0)
- ZXing library integration
- Material Design components
- Google Play Services integration

---

## Version Numbering

This project follows [Semantic Versioning](https://semver.org/):
- **MAJOR** version for incompatible API changes
- **MINOR** version for backwards-compatible functionality additions
- **PATCH** version for backwards-compatible bug fixes

## Support

For questions about specific versions or changes, please:
1. Check the documentation
2. Search existing issues
3. Create a new issue with version details

---

**Note**: This changelog follows the [Keep a Changelog](https://keepachangelog.com/) format.
