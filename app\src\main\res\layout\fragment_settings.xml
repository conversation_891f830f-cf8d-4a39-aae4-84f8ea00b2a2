<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/prefs_content"
    android:orientation="vertical" android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@+id/app_version"
            android:layout_alignParentStart="true"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/general"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/general_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="15dp"
                    android:paddingTop="5dp"
                    android:paddingRight="15dp"
                    android:paddingBottom="5dp"
                    android:text="@string/general"
                    android:textAlignment="textStart"
                    android:textAllCaps="true"
                    android:textColor="@color/colorPrimaryDark"
                    android:textSize="18sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <Switch
                android:id="@+id/switch_vibrate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:checked="true"
                android:paddingLeft="15dp"
                android:paddingTop="12dp"
                android:paddingRight="15dp"
                android:paddingBottom="12dp"
                android:text="@string/settings_vibrate"
                android:textSize="18sp" />

            <ImageView
                android:id="@+id/line2"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:paddingLeft="15dp"
                android:paddingRight="15dp"
                app:srcCompat="@color/materialcolorpicker__lightgrey" />

            <Switch
                android:id="@+id/switch_autofocus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:checked="true"
                android:paddingLeft="15dp"
                android:paddingTop="12dp"
                android:paddingRight="15dp"
                android:paddingBottom="12dp"
                android:text="@string/settings_autofocus"
                android:textSize="18sp" />

            <ImageView
                android:id="@+id/line3"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:paddingLeft="15dp"
                android:paddingRight="15dp"
                app:srcCompat="@color/materialcolorpicker__lightgrey" />

            <Switch
                android:id="@+id/switch_auto_url"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingLeft="15dp"
                android:paddingTop="12dp"
                android:paddingRight="15dp"
                android:paddingBottom="12dp"
                android:text="@string/settings_auto_links"
                android:textSize="18sp" />

            <ImageView
                android:id="@+id/line5"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:paddingLeft="15dp"
                android:paddingRight="15dp"
                app:srcCompat="@color/materialcolorpicker__lightgrey" />

            <LinearLayout
                android:id="@+id/other"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/other_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="15dp"
                    android:paddingTop="5dp"
                    android:paddingRight="15dp"
                    android:paddingBottom="5dp"
                    android:text="@string/other"
                    android:textAlignment="textStart"
                    android:textAllCaps="true"
                    android:textColor="@color/colorPrimaryDark"
                    android:textSize="18sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/rate_us"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingRight="20dp">

                <TextView
                    android:id="@+id/rate_us_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:paddingLeft="15dp"
                    android:paddingTop="12dp"
                    android:paddingRight="15dp"
                    android:paddingBottom="12dp"
                    android:text="@string/rate_us"
                    android:textAllCaps="false"
                    android:textColor="@color/materialcolorpicker__black"
                    android:textSize="18sp" />

                <ImageView
                    android:id="@+id/rate__icon"
                    android:layout_width="65dp"
                    android:layout_height="32dp"
                    android:layout_weight="1"
                    android:tint="@color/materialcolorpicker__lightgrey"
                    app:srcCompat="@drawable/rate_us" />
            </LinearLayout>

            <ImageView
                android:id="@+id/line6"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:paddingLeft="15dp"
                android:paddingRight="15dp"
                app:srcCompat="@color/materialcolorpicker__lightgrey" />

            <LinearLayout
                android:id="@+id/share_app"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingRight="20dp">

                <TextView
                    android:id="@+id/share_app_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:paddingLeft="15dp"
                    android:paddingTop="12dp"
                    android:paddingRight="15dp"
                    android:paddingBottom="12dp"
                    android:text="@string/share_app"
                    android:textAllCaps="false"
                    android:textColor="@color/materialcolorpicker__black"
                    android:textSize="18sp" />

                <ImageView
                    android:id="@+id/share_icon"
                    android:layout_width="65dp"
                    android:layout_height="32dp"
                    android:layout_weight="1"
                    android:tint="@color/materialcolorpicker__lightgrey"
                    app:srcCompat="@drawable/ic_share" />
            </LinearLayout>

            <ImageView
                android:id="@+id/line7"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:paddingLeft="15dp"
                android:paddingRight="15dp"
                app:srcCompat="@color/materialcolorpicker__lightgrey" />

        </LinearLayout>
    </ScrollView>

    <TextView
        android:id="@+id/app_version"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/scroll_view"
        android:layout_alignParentStart="true"
        android:layout_alignParentBottom="true"
        android:layout_weight="1"
        android:foregroundGravity="bottom"
        android:gravity="bottom"
        android:text="@string/version"
        android:textAlignment="center"
        android:textSize="16sp" />

</RelativeLayout>