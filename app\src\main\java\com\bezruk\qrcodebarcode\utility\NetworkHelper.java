package com.bezruk.qrcodebarcode.utility;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.os.Build;
import android.util.Log;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.TimeUnit;

/**
 * Network utility helper for connectivity and URL operations
 */
public class NetworkHelper {
    
    private static final String TAG = "NetworkHelper";
    private static final int CONNECTION_TIMEOUT = 10000; // 10 seconds
    private static final int READ_TIMEOUT = 15000; // 15 seconds
    
    /**
     * Check if device has internet connectivity
     */
    public static boolean isNetworkAvailable(Context context) {
        ConnectivityManager connectivityManager = 
            (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        
        if (connectivityManager == null) {
            return false;
        }
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Network network = connectivityManager.getActiveNetwork();
            if (network == null) return false;
            
            NetworkCapabilities capabilities = connectivityManager.getNetworkCapabilities(network);
            return capabilities != null && (
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) ||
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)
            );
        } else {
            NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
            return networkInfo != null && networkInfo.isConnected();
        }
    }
    
    /**
     * Check if connected to WiFi
     */
    public static boolean isWiFiConnected(Context context) {
        ConnectivityManager connectivityManager = 
            (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        
        if (connectivityManager == null) {
            return false;
        }
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Network network = connectivityManager.getActiveNetwork();
            if (network == null) return false;
            
            NetworkCapabilities capabilities = connectivityManager.getNetworkCapabilities(network);
            return capabilities != null && capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI);
        } else {
            NetworkInfo wifiInfo = connectivityManager.getNetworkInfo(ConnectivityManager.TYPE_WIFI);
            return wifiInfo != null && wifiInfo.isConnected();
        }
    }
    
    /**
     * Check if connected to mobile data
     */
    public static boolean isMobileDataConnected(Context context) {
        ConnectivityManager connectivityManager = 
            (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        
        if (connectivityManager == null) {
            return false;
        }
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Network network = connectivityManager.getActiveNetwork();
            if (network == null) return false;
            
            NetworkCapabilities capabilities = connectivityManager.getNetworkCapabilities(network);
            return capabilities != null && capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR);
        } else {
            NetworkInfo mobileInfo = connectivityManager.getNetworkInfo(ConnectivityManager.TYPE_MOBILE);
            return mobileInfo != null && mobileInfo.isConnected();
        }
    }
    
    /**
     * Get network type as string
     */
    public static String getNetworkType(Context context) {
        if (isWiFiConnected(context)) {
            return "WiFi";
        } else if (isMobileDataConnected(context)) {
            return "Mobile Data";
        } else if (isNetworkAvailable(context)) {
            return "Other";
        } else {
            return "No Connection";
        }
    }
    
    /**
     * Check if URL is reachable
     */
    public static boolean isUrlReachable(String urlString) {
        return isUrlReachable(urlString, CONNECTION_TIMEOUT);
    }
    
    /**
     * Check if URL is reachable with custom timeout
     */
    public static boolean isUrlReachable(String urlString, int timeoutMs) {
        try {
            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(timeoutMs);
            connection.setReadTimeout(timeoutMs);
            connection.setInstanceFollowRedirects(true);
            
            // Set user agent to avoid blocking
            connection.setRequestProperty("User-Agent", 
                "Mozilla/5.0 (Android; Mobile; rv:40.0) Gecko/40.0 Firefox/40.0");
            
            int responseCode = connection.getResponseCode();
            connection.disconnect();
            
            return responseCode >= 200 && responseCode < 400;
        } catch (Exception e) {
            Log.w(TAG, "URL not reachable: " + urlString, e);
            return false;
        }
    }
    
    /**
     * Validate URL format
     */
    public static boolean isValidUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }
        
        try {
            new URL(url);
            return url.startsWith("http://") || url.startsWith("https://");
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Extract domain from URL
     */
    public static String extractDomain(String url) {
        try {
            URL urlObj = new URL(url);
            return urlObj.getHost();
        } catch (Exception e) {
            Log.w(TAG, "Failed to extract domain from: " + url, e);
            return null;
        }
    }
    
    /**
     * Check if URL is HTTPS
     */
    public static boolean isHttpsUrl(String url) {
        return url != null && url.toLowerCase().startsWith("https://");
    }
    
    /**
     * Convert HTTP URL to HTTPS if possible
     */
    public static String upgradeToHttps(String url) {
        if (url != null && url.toLowerCase().startsWith("http://")) {
            return url.replaceFirst("(?i)^http://", "https://");
        }
        return url;
    }
    
    /**
     * Get connection quality based on network type
     */
    public static ConnectionQuality getConnectionQuality(Context context) {
        if (!isNetworkAvailable(context)) {
            return ConnectionQuality.NO_CONNECTION;
        }
        
        if (isWiFiConnected(context)) {
            return ConnectionQuality.EXCELLENT;
        } else if (isMobileDataConnected(context)) {
            return ConnectionQuality.GOOD;
        } else {
            return ConnectionQuality.POOR;
        }
    }
    
    /**
     * Connection quality enum
     */
    public enum ConnectionQuality {
        NO_CONNECTION(0),
        POOR(1),
        GOOD(2),
        EXCELLENT(3);
        
        private final int value;
        
        ConnectionQuality(int value) {
            this.value = value;
        }
        
        public int getValue() {
            return value;
        }
        
        public boolean isGoodEnough() {
            return value >= GOOD.value;
        }
    }
    
    /**
     * Network callback interface
     */
    public interface NetworkCallback {
        void onNetworkAvailable();
        void onNetworkLost();
    }
    
    /**
     * Simple network monitor
     */
    public static class NetworkMonitor {
        private ConnectivityManager connectivityManager;
        private NetworkCallback callback;
        private ConnectivityManager.NetworkCallback networkCallback;
        
        public NetworkMonitor(Context context, NetworkCallback callback) {
            this.connectivityManager = (ConnectivityManager) 
                context.getSystemService(Context.CONNECTIVITY_SERVICE);
            this.callback = callback;
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                this.networkCallback = new ConnectivityManager.NetworkCallback() {
                    @Override
                    public void onAvailable(Network network) {
                        if (NetworkMonitor.this.callback != null) {
                            NetworkMonitor.this.callback.onNetworkAvailable();
                        }
                    }
                    
                    @Override
                    public void onLost(Network network) {
                        if (NetworkMonitor.this.callback != null) {
                            NetworkMonitor.this.callback.onNetworkLost();
                        }
                    }
                };
            }
        }
        
        public void startMonitoring() {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N && 
                connectivityManager != null && networkCallback != null) {
                connectivityManager.registerDefaultNetworkCallback(networkCallback);
            }
        }
        
        public void stopMonitoring() {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N && 
                connectivityManager != null && networkCallback != null) {
                connectivityManager.unregisterNetworkCallback(networkCallback);
            }
        }
    }
}
