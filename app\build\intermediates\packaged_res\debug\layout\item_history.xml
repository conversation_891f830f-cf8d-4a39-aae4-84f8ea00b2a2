<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/item_history_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackground"
        android:clickable="true"
        android:orientation="horizontal"
        android:weightSum="0">

        <ImageView
            android:id="@+id/actionIcon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="top|center_vertical"
            android:layout_marginLeft="5dp"
            android:layout_marginTop="5dp"
            android:layout_weight=".0"
            android:padding="7dp"
            android:src="@drawable/ic_web"
            android:tint="@color/colorPrimary" />


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight=".9"
            android:orientation="vertical">

            <TextView
                android:id="@+id/result"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight=".7"
                android:gravity="center_vertical"
                android:paddingLeft="5dp"
                android:paddingTop="15dp"
                android:paddingRight="5dp"
                android:paddingBottom="5dp"
                android:text="@string/action"
                android:textColor="@color/colorAccent"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/date"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:layout_marginBottom="5dp"
                android:layout_weight=".7"
                android:gravity="center_vertical"
                android:text="@string/date"
                android:textSize="14sp" />

        </LinearLayout>

        <ImageButton
            android:id="@+id/deleteButton"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="center_vertical"
            android:layout_weight=".0"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:focusable="false"
            android:padding="10dp"
            android:scaleType="centerInside"
            android:src="@drawable/ic_delete"
            android:tint="@color/colorAccent" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/viewfinder_mask" />

</LinearLayout>