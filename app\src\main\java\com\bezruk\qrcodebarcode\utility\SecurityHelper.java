package com.bezruk.qrcodebarcode.utility;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.Signature;
import android.os.Build;
import android.provider.Settings;
import android.util.Base64;
import android.util.Log;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.regex.Pattern;

/**
 * Security helper for app protection and validation
 */
public class SecurityHelper {
    
    private static final String TAG = "SecurityHelper";
    
    // Common malicious URL patterns
    private static final Pattern SUSPICIOUS_URL_PATTERN = Pattern.compile(
        "(?i)(javascript:|data:|vbscript:|file:|ftp://|about:|chrome:|res:|android_asset:|android_res:)"
    );
    
    // SQL injection patterns
    private static final Pattern SQL_INJECTION_PATTERN = Pattern.compile(
        "(?i)(union|select|insert|update|delete|drop|create|alter|exec|script|javascript|vbscript)"
    );
    
    /**
     * Validate if URL is safe to open
     */
    public static boolean isSafeUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }
        
        // Check for suspicious protocols
        if (SUSPICIOUS_URL_PATTERN.matcher(url).find()) {
            Log.w(TAG, "Suspicious URL detected: " + url);
            return false;
        }
        
        // Check for common malicious patterns
        String lowerUrl = url.toLowerCase();
        if (lowerUrl.contains("javascript:") || 
            lowerUrl.contains("data:") || 
            lowerUrl.contains("vbscript:")) {
            return false;
        }
        
        // Basic URL validation
        return url.startsWith("http://") || url.startsWith("https://");
    }
    
    /**
     * Sanitize text input to prevent injection attacks
     */
    public static String sanitizeInput(String input) {
        if (input == null) {
            return "";
        }
        
        // Remove potential SQL injection patterns
        String sanitized = input.replaceAll("(?i)(union|select|insert|update|delete|drop|create|alter|exec)", "");
        
        // Remove script tags and javascript
        sanitized = sanitized.replaceAll("(?i)<script[^>]*>.*?</script>", "");
        sanitized = sanitized.replaceAll("(?i)javascript:", "");
        sanitized = sanitized.replaceAll("(?i)vbscript:", "");
        
        // Remove potentially dangerous characters
        sanitized = sanitized.replaceAll("[<>\"'&]", "");
        
        return sanitized.trim();
    }
    
    /**
     * Validate phone number format
     */
    public static boolean isValidPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            return false;
        }
        
        // Remove common formatting characters
        String cleanNumber = phoneNumber.replaceAll("[\\s\\-\\(\\)\\+]", "");
        
        // Check if it contains only digits and is reasonable length
        return cleanNumber.matches("\\d{7,15}");
    }
    
    /**
     * Validate email format
     */
    public static boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        
        Pattern emailPattern = Pattern.compile(
            "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
        );
        
        return emailPattern.matcher(email).matches();
    }
    
    /**
     * Check if device is rooted (basic check)
     */
    public static boolean isDeviceRooted() {
        // Check for common root indicators
        String[] rootIndicators = {
            "/system/app/Superuser.apk",
            "/sbin/su",
            "/system/bin/su",
            "/system/xbin/su",
            "/data/local/xbin/su",
            "/data/local/bin/su",
            "/system/sd/xbin/su",
            "/system/bin/failsafe/su",
            "/data/local/su"
        };
        
        for (String indicator : rootIndicators) {
            if (new java.io.File(indicator).exists()) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check if app is installed from official store
     */
    public static boolean isInstalledFromOfficialStore(Context context) {
        try {
            String installer = context.getPackageManager()
                .getInstallerPackageName(context.getPackageName());
            
            return installer != null && (
                installer.equals("com.android.vending") || // Google Play Store
                installer.equals("com.amazon.venezia") ||   // Amazon Appstore
                installer.equals("com.sec.android.app.samsungapps") // Samsung Galaxy Store
            );
        } catch (Exception e) {
            Log.e(TAG, "Error checking installer", e);
            return false;
        }
    }
    
    /**
     * Get app signature hash for integrity verification
     */
    public static String getAppSignatureHash(Context context) {
        try {
            PackageInfo packageInfo;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                packageInfo = context.getPackageManager()
                    .getPackageInfo(context.getPackageName(), PackageManager.GET_SIGNING_CERTIFICATES);
                Signature[] signatures = packageInfo.signingInfo.getApkContentsSigners();
                return getSignatureHash(signatures[0]);
            } else {
                packageInfo = context.getPackageManager()
                    .getPackageInfo(context.getPackageName(), PackageManager.GET_SIGNATURES);
                return getSignatureHash(packageInfo.signatures[0]);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting signature hash", e);
            return null;
        }
    }
    
    private static String getSignatureHash(Signature signature) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            md.update(signature.toByteArray());
            return Base64.encodeToString(md.digest(), Base64.NO_WRAP);
        } catch (NoSuchAlgorithmException e) {
            Log.e(TAG, "Error creating signature hash", e);
            return null;
        }
    }
    
    /**
     * Check if device has developer options enabled
     */
    public static boolean isDeveloperOptionsEnabled(Context context) {
        return Settings.Global.getInt(context.getContentResolver(),
            Settings.Global.DEVELOPMENT_SETTINGS_ENABLED, 0) != 0;
    }
    
    /**
     * Check if USB debugging is enabled
     */
    public static boolean isUsbDebuggingEnabled(Context context) {
        return Settings.Global.getInt(context.getContentResolver(),
            Settings.Global.ADB_ENABLED, 0) != 0;
    }
    
    /**
     * Validate QR code content for safety
     */
    public static boolean isSafeQRContent(String content) {
        if (content == null || content.trim().isEmpty()) {
            return false;
        }
        
        // Check for excessively long content (potential DoS)
        if (content.length() > 10000) {
            Log.w(TAG, "QR content too long: " + content.length());
            return false;
        }
        
        // Check for suspicious patterns
        if (SQL_INJECTION_PATTERN.matcher(content).find()) {
            Log.w(TAG, "Suspicious QR content detected");
            return false;
        }
        
        return true;
    }
    
    /**
     * Generate secure random string
     */
    public static String generateSecureRandomString(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        java.security.SecureRandom random = new java.security.SecureRandom();
        StringBuilder sb = new StringBuilder();
        
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        
        return sb.toString();
    }
    
    /**
     * Hash sensitive data
     */
    public static String hashData(String data) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(data.getBytes("UTF-8"));
            return Base64.encodeToString(hash, Base64.NO_WRAP);
        } catch (Exception e) {
            Log.e(TAG, "Error hashing data", e);
            return null;
        }
    }
}
