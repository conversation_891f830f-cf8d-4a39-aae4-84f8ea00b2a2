<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="DialogTheme" parent="Theme.AppCompat.Light.Dialog.Alert"/>
    <declare-styleable name="BarcodeScannerView">
        <attr format="boolean" name="shouldScaleToFill"/>
        <attr format="boolean" name="laserEnabled"/>
        <attr format="color" name="laserColor"/>
        <attr format="color" name="borderColor"/>
        <attr format="color" name="maskColor"/>
        <attr format="dimension" name="borderWidth"/>
        <attr format="dimension" name="borderLength"/>
        <attr format="boolean" name="roundedCorner"/>
        <attr format="dimension" name="cornerRadius"/>
        <attr format="boolean" name="squaredFinder"/>
        <attr format="float" name="borderAlpha"/>
        <attr format="dimension" name="finderOffset"/>
    </declare-styleable>
</resources>