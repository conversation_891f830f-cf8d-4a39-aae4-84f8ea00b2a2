R_DEF: Internal format may change without notice
local
array wifi_security_list
attr? borderAlpha
attr? borderColor
attr? borderLength
attr? borderWidth
attr colorAccent
attr colorPrimary
attr colorPrimaryDark
attr? cornerRadius
attr? finderOffset
attr? laserColor
attr? laserEnabled
attr? maskColor
attr? roundedCorner
attr? shouldScaleToFill
attr? squaredFinder
color colorAccent
color colorPrimary
color colorPrimaryDark
color darkMainColor
color fab
color grey
color light_grey_for_setting
color materialcolorpicker__black
color materialcolorpicker__blue
color materialcolorpicker__brown
color materialcolorpicker__cyan
color materialcolorpicker__darkgrey
color materialcolorpicker__green
color materialcolorpicker__grey
color materialcolorpicker__lightgrey
color materialcolorpicker__orange
color materialcolorpicker__pink
color materialcolorpicker__purple
color materialcolorpicker__red
color materialcolorpicker__white
color materialcolorpicker__yellow
color viewfinder_border
color viewfinder_laser
color viewfinder_mask
color white
dimen fab_margin
dimen fab_margin_16
drawable bottom_navigation_item_background_colors
drawable custom_input
drawable ic_bar_placeholder
drawable ic_barcode
drawable ic_barcode_button
drawable ic_barcode_white
drawable ic_call
drawable ic_call_white
drawable ic_color
drawable ic_contact
drawable ic_contact_button
drawable ic_contact_plus_btn
drawable ic_contact_white
drawable ic_copy
drawable ic_copy_white
drawable ic_delete
drawable ic_delete_all
drawable ic_email
drawable ic_email_white
drawable ic_flash_off
drawable ic_flash_on
drawable ic_focus_off
drawable ic_focus_on
drawable ic_gallery_scan
drawable ic_generate
drawable ic_history
drawable ic_launcher
drawable ic_location
drawable ic_plain_text
drawable ic_plain_text_white
drawable ic_qr_button
drawable ic_qr_placeholder
drawable ic_qr_scan
drawable ic_save
drawable ic_search_web
drawable ic_search_web_white
drawable ic_setting
drawable ic_share
drawable ic_share_result
drawable ic_sms
drawable ic_sms_white
drawable ic_switch_cam
drawable ic_three_dot
drawable ic_vcard
drawable ic_video
drawable ic_web
drawable ic_web_white
drawable ic_wifi
drawable ic_wifi_white
drawable qr_bg1
drawable qr_bg2
drawable qr_bg3
drawable qr_bg4
drawable qr_bg5
drawable qr_bg6
drawable qr_bg7
drawable rate_us
drawable splash_icon
id BtnLayout
id TopLayout
id TopScrollBtn
id Top_btns
id action1_result_btn
id action2_result_btn
id action3_result_btn
id action4_result_btn
id actionIcon
id action_camera
id action_flash
id action_focus
id adViewMain
id adViewResult
id allowCameraUseBtn
id app_version
id barcode_btn
id btn_for_qr_code_layout
id camera
id color
id color_of_result_qrcode_btn
id contact_btn
id contact_choose_btn
id content_frame
id copy_result_btn
id created_res_btn
id date
id deleteAll
id deleteButton
id email_btn
id flash
id gallery
id general
id general_text
id generate_btn
id inputLayout
id inputLayout2
id inputLayout3
id inputLayout4
id inputLayout5
id inputText
id inputText2
id inputText3
id inputText4
id item_history_layout
id line
id line2
id line3
id line5
id line6
id line7
id nav_generate
id nav_history
id nav_scan
id nav_setting
id navigation
id noResultView
id okColorButton
id other
id other_text
id outputBitmap
id prefs_content
id qr_code_result_layout
id rate__icon
id rate_us
id rate_us_text
id recycler_view
id result
id resultIcon
id result_btns_layout
id result_qr_code_img
id save
id save_of_result_qrcode_btn
id scanned_res_btn
id scanned_result_tile
id scanned_result_type_of_code
id scroll_view
id share
id share_app
id share_app_text
id share_icon
id share_of_result_qrcode_btn
id share_result_btn
id sms_btn
id splashBody
id splashIcon
id switch_auto_url
id switch_autofocus
id switch_vibrate
id text_btn
id toolbar
id top_btn
id url_btn
id viewpager
id wifi_btn
id wifi_type
integer viewfinder_border_length
integer viewfinder_border_width
layout activity_main
layout activity_result
layout activity_splash
layout content_result
layout for_fragment_history_fragment_generate
layout for_fragment_history_fragment_scan
layout fragment_generate
layout fragment_history
layout fragment_scan
layout fragment_settings
layout item_history
menu bottom_navigation
menu menu_main
string action
string action_barcode
string action_call
string action_cam
string action_email
string action_flash
string action_focus
string action_visit
string action_youtube
string allowCameraUse
string app_ad_id
string app_name
string autofocus_off
string autofocus_on
string banner_ad_unit_id
string copied
string copy
string date
string delete_message_all
string delete_message_item
string directory_in
string error_file
string error_format
string error_no_email_app
string error_nothing_find
string error_restart_app
string error_unexpected
string error_unknown
string error_wifi
string general
string interstitial_ad_unit_id
string menu_generate
string menu_history
string menu_scan
string menu_setting
string no
string no_history
string other
string permission_not_granted
string preparing
string rate_menu_maybe_btn
string rate_menu_message
string rate_menu_no_btn
string rate_menu_title
string rate_menu_yes_btn
string rate_us
string remove_ads
string restore_ads
string result
string result_barcode
string result_email
string result_geo
string result_phone
string result_sms
string result_text
string result_url
string result_vcard
string result_wifi
string result_youtube
string saved_to
string saving
string scanned_result
string scanned_type_barcode
string scanned_type_qrcode
string search
string settings_auto_links
string settings_autofocus
string settings_vibrate
string share
string share_app
string share_text
string splash_text
string tapAgain
string type_here_address
string type_here_bar
string type_here_barcode
string type_here_email
string type_here_email_subject
string type_here_name
string type_here_phone
string type_here_qr
string type_here_sms_phone_number
string type_here_sms_text
string type_here_text
string type_here_url
string type_here_wifi_password
string type_here_wifi_ssid
string version
string yes
style AppTheme
style AppTheme.AppBarOverlay
style AppTheme.NoActionBar
style AppTheme.PopupOverlay
style DialogTheme
styleable BarcodeScannerView shouldScaleToFill laserEnabled laserColor borderColor maskColor borderWidth borderLength roundedCorner cornerRadius squaredFinder borderAlpha finderOffset
xml backup_rules
xml data_extraction_rules
xml file_paths
