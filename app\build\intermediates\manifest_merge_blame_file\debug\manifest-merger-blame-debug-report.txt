1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.bezruk.qrcodebarcode"
4    android:versionCode="7"
5    android:versionName="1.0.6" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="33" />
10
11    <!-- Camera permission for QR/Barcode scanning -->
12    <uses-permission android:name="android.permission.CAMERA" />
12-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:6:5-65
12-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:6:22-62
13
14    <!-- Storage permissions with proper scoped storage handling -->
15    <uses-permission
15-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:9:5-10:38
16        android:name="android.permission.READ_EXTERNAL_STORAGE"
16-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:9:22-77
17        android:maxSdkVersion="32" />
17-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:10:9-35
18    <uses-permission
18-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:11:5-13:40
19        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
19-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:11:22-78
20        android:maxSdkVersion="29" />
20-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:12:9-35
21    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
21-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:14:5-76
21-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:14:22-73
22
23    <!-- Vibration for feedback -->
24    <uses-permission android:name="android.permission.VIBRATE" />
24-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:17:5-66
24-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:17:22-63
25
26    <!-- Phone permission for calling -->
27    <uses-permission android:name="android.permission.CALL_PHONE" />
27-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:20:5-69
27-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:20:22-66
28
29    <!-- Contacts permission -->
30    <uses-permission android:name="android.permission.READ_CONTACTS" />
30-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:23:5-72
30-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:23:22-69
31
32    <!-- WiFi permissions -->
33    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
33-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:26:5-76
33-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:26:22-73
34    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
34-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:27:5-76
34-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:27:22-73
35
36    <!-- Internet for ads -->
37    <uses-permission android:name="android.permission.INTERNET" />
37-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:30:5-67
37-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:30:22-64
38    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
38-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:31:5-79
38-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:31:22-76
39
40    <!-- Billing permission -->
41    <uses-permission android:name="com.android.vending.BILLING" />
41-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:34:5-67
41-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:34:22-64
42
43    <!-- Camera features -->
44    <uses-feature
44-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:37:5-39:35
45        android:name="android.hardware.camera"
45-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:38:9-47
46        android:required="true" />
46-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:39:9-32
47    <uses-feature
47-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:40:5-42:36
48        android:name="android.hardware.camera.autofocus"
48-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:41:9-57
49        android:required="false" />
49-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:42:9-33
50    <uses-feature
50-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:43:5-45:36
51        android:name="android.hardware.camera.flash"
51-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:44:9-53
52        android:required="false" />
52-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:45:9-33
53    <uses-feature
53-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
54        android:name="android.hardware.camera.front"
54-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
55        android:required="false" />
55-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
56    <uses-feature
56-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
57        android:name="android.hardware.screen.landscape"
57-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
58        android:required="false" />
58-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
59    <uses-feature
59-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
60        android:name="android.hardware.wifi"
60-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
61        android:required="false" />
61-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
62
63    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
63-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:26:5-79
63-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:26:22-76
64    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
64-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:27:5-82
64-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:27:22-79
65    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
65-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:28:5-88
65-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:28:22-85
66    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Android package visibility setting -->
66-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:29:5-83
66-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:29:22-80
67    <queries>
67-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:35:5-51:15
68
69        <!-- For browser content -->
70        <intent>
70-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:38:9-44:18
71            <action android:name="android.intent.action.VIEW" />
71-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:39:13-65
71-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:39:21-62
72
73            <category android:name="android.intent.category.BROWSABLE" />
73-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:41:13-74
73-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:41:23-71
74
75            <data android:scheme="https" />
75-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:43:13-44
75-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:43:19-41
76        </intent>
77        <!-- End of browser content -->
78        <!-- For CustomTabsService -->
79        <intent>
79-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:47:9-49:18
80            <action android:name="android.support.customtabs.action.CustomTabsService" />
80-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:48:13-90
80-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:48:21-87
81        </intent>
82        <!-- End of CustomTabsService -->
83    </queries>
84
85    <uses-permission android:name="android.permission.WAKE_LOCK" />
85-->[com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\841e3cab8a4af71928abf9f5a0c9ceda\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:5-68
85-->[com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\841e3cab8a4af71928abf9f5a0c9ceda\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:22-65
86    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
86-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
86-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
87
88    <permission
88-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
89        android:name="com.bezruk.qrcodebarcode.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
89-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
90        android:protectionLevel="signature" />
90-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
91
92    <uses-permission android:name="com.bezruk.qrcodebarcode.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
92-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
92-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
93
94    <application
94-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:47:5-101:19
95        android:allowBackup="true"
95-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:48:9-35
96        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
96-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\75919e89ab9acfe92b320b18b5eba894\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
97        android:dataExtractionRules="@xml/data_extraction_rules"
97-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:49:9-65
98        android:debuggable="true"
99        android:extractNativeLibs="true"
100        android:fullBackupContent="@xml/backup_rules"
100-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:50:9-54
101        android:hardwareAccelerated="true"
101-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:55:9-43
102        android:icon="@drawable/ic_launcher"
102-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:51:9-45
103        android:label="@string/app_name"
103-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:52:9-41
104        android:largeHeap="true"
104-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:56:9-33
105        android:requestLegacyExternalStorage="true"
105-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:57:9-52
106        android:supportsRtl="true"
106-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:53:9-35
107        android:theme="@style/AppTheme" >
107-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:54:9-40
108        <activity
108-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:60:9-70:20
109            android:name="com.bezruk.qrcodebarcode.activity.SplashActivity"
109-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:61:13-52
110            android:exported="true"
110-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:65:13-36
111            android:label="@string/app_name"
111-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:62:13-45
112            android:screenOrientation="portrait"
112-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:64:13-49
113            android:theme="@style/AppTheme.NoActionBar" >
113-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:63:13-56
114            <intent-filter>
114-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:66:13-69:29
115                <action android:name="android.intent.action.MAIN" />
115-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:67:17-69
115-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:67:25-66
116
117                <category android:name="android.intent.category.LAUNCHER" />
117-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:68:17-77
117-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:68:27-74
118            </intent-filter>
119        </activity>
120        <activity
120-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:72:9-77:20
121            android:name="com.bezruk.qrcodebarcode.activity.MainActivity"
121-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:73:13-50
122            android:exported="false"
122-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:76:13-37
123            android:screenOrientation="portrait"
123-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:74:13-49
124            android:theme="@style/AppTheme.NoActionBar" >
124-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:75:13-56
125        </activity>
126        <activity
126-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:79:9-83:39
127            android:name="com.bezruk.qrcodebarcode.activity.ResultActivity"
127-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:80:13-52
128            android:exported="false"
128-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:83:13-37
129            android:screenOrientation="portrait"
129-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:81:13-49
130            android:theme="@style/AppTheme.NoActionBar" />
130-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:82:13-56
131
132        <!-- Google AdMob App ID -->
133        <meta-data
133-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:86:9-88:69
134            android:name="com.google.android.gms.ads.APPLICATION_ID"
134-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:87:13-69
135            android:value="ca-app-pub-3940256099942544~**********" />
135-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:88:13-67
136
137        <!-- FileProvider for sharing files -->
138        <provider
139            android:name="androidx.core.content.FileProvider"
139-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:92:13-62
140            android:authorities="com.bezruk.qrcodebarcode.fileprovider"
140-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:93:13-64
141            android:exported="false"
141-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:95:13-37
142            android:grantUriPermissions="true" >
142-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:94:13-47
143            <meta-data
143-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:96:13-98:54
144                android:name="android.support.FILE_PROVIDER_PATHS"
144-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:97:17-67
145                android:resource="@xml/file_paths" />
145-->C:\Users\<USER>\Desktop\Projects\QRscannerMAX\app\src\main\AndroidManifest.xml:98:17-51
146        </provider>
147
148        <activity
148-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
149            android:name="com.journeyapps.barcodescanner.CaptureActivity"
149-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
150            android:clearTaskOnLaunch="true"
150-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
151            android:screenOrientation="sensorLandscape"
151-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
152            android:stateNotNeeded="true"
152-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
153            android:theme="@style/zxing_CaptureTheme"
153-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
154            android:windowSoftInputMode="stateAlwaysHidden" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
154-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\21e415e6faa3be34738048e71349b8b6\transformed\jetified-zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
155        <activity
155-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:56:9-61:43
156            android:name="com.google.android.gms.ads.AdActivity"
156-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:57:13-65
157            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
157-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:58:13-122
158            android:exported="false"
158-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:59:13-37
159            android:theme="@android:style/Theme.Translucent" />
159-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:60:13-61
160
161        <provider
161-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:63:9-68:43
162            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
162-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:64:13-76
163            android:authorities="com.bezruk.qrcodebarcode.mobileadsinitprovider"
163-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:65:13-73
164            android:exported="false"
164-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:66:13-37
165            android:initOrder="100" />
165-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:67:13-36
166
167        <service
167-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:70:9-74:43
168            android:name="com.google.android.gms.ads.AdService"
168-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:71:13-64
169            android:enabled="true"
169-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:72:13-35
170            android:exported="false" />
170-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:73:13-37
171
172        <activity
172-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:76:9-80:43
173            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
173-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:77:13-82
174            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
174-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:78:13-122
175            android:exported="false" />
175-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:79:13-37
176        <activity
176-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:81:9-88:43
177            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
177-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:82:13-82
178            android:excludeFromRecents="true"
178-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:83:13-46
179            android:exported="false"
179-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:84:13-37
180            android:launchMode="singleTask"
180-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:85:13-44
181            android:taskAffinity=""
181-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:86:13-36
182            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
182-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:87:13-72
183
184        <property
184-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:90:9-92:62
185            android:name="android.adservices.AD_SERVICES_CONFIG"
185-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:91:13-65
186            android:resource="@xml/gma_ad_services_config" />
186-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a2f96c8d3914741b46043f16b1e08cf\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:92:13-59
187
188        <activity
188-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ede285f672e73ef59c1f7bc9b2e633f9\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:20:9-22:45
189            android:name="com.google.android.gms.common.api.GoogleApiActivity"
189-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ede285f672e73ef59c1f7bc9b2e633f9\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:20:19-85
190            android:exported="false"
190-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ede285f672e73ef59c1f7bc9b2e633f9\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:22:19-43
191            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
191-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ede285f672e73ef59c1f7bc9b2e633f9\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:21:19-78
192
193        <meta-data
193-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\034e53a5369349e96a448cde5bcf995e\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
194            android:name="com.google.android.gms.version"
194-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\034e53a5369349e96a448cde5bcf995e\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
195            android:value="@integer/google_play_services_version" />
195-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\034e53a5369349e96a448cde5bcf995e\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
196
197        <provider
197-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4381fccdde393d19d04161b40313d198\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
198            android:name="androidx.startup.InitializationProvider"
198-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4381fccdde393d19d04161b40313d198\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
199            android:authorities="com.bezruk.qrcodebarcode.androidx-startup"
199-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4381fccdde393d19d04161b40313d198\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
200            android:exported="false" >
200-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4381fccdde393d19d04161b40313d198\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
201            <meta-data
201-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4381fccdde393d19d04161b40313d198\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
202                android:name="androidx.emoji2.text.EmojiCompatInitializer"
202-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4381fccdde393d19d04161b40313d198\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
203                android:value="androidx.startup" />
203-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4381fccdde393d19d04161b40313d198\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
204            <meta-data
204-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
205                android:name="androidx.work.WorkManagerInitializer"
205-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
206                android:value="androidx.startup" />
206-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
207            <meta-data
207-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\818eda85d3b5ca5901b62e2194b4fd5f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
208                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
208-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\818eda85d3b5ca5901b62e2194b4fd5f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
209                android:value="androidx.startup" />
209-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\818eda85d3b5ca5901b62e2194b4fd5f\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
210            <meta-data
210-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
211                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
211-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
212                android:value="androidx.startup" />
212-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
213        </provider>
214
215        <uses-library
215-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\63caa8bf6109ecbe6af1511e60399f08\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
216            android:name="android.ext.adservices"
216-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\63caa8bf6109ecbe6af1511e60399f08\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
217            android:required="false" />
217-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\63caa8bf6109ecbe6af1511e60399f08\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
218
219        <service
219-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
220            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
220-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
221            android:directBootAware="false"
221-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
222            android:enabled="@bool/enable_system_alarm_service_default"
222-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
223            android:exported="false" />
223-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
224        <service
224-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
225            android:name="androidx.work.impl.background.systemjob.SystemJobService"
225-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
226            android:directBootAware="false"
226-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
227            android:enabled="@bool/enable_system_job_service_default"
227-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
228            android:exported="true"
228-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
229            android:permission="android.permission.BIND_JOB_SERVICE" />
229-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
230        <service
230-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
231            android:name="androidx.work.impl.foreground.SystemForegroundService"
231-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
232            android:directBootAware="false"
232-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
233            android:enabled="@bool/enable_system_foreground_service_default"
233-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
234            android:exported="false" />
234-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
235
236        <receiver
236-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
237            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
237-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
238            android:directBootAware="false"
238-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
239            android:enabled="true"
239-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
240            android:exported="false" />
240-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
241        <receiver
241-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
242            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
242-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
243            android:directBootAware="false"
243-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
244            android:enabled="false"
244-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
245            android:exported="false" >
245-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
246            <intent-filter>
246-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
247                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
247-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
247-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
248                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
248-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
248-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
249            </intent-filter>
250        </receiver>
251        <receiver
251-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
252            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
252-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
253            android:directBootAware="false"
253-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
254            android:enabled="false"
254-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
255            android:exported="false" >
255-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
256            <intent-filter>
256-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
257                <action android:name="android.intent.action.BATTERY_OKAY" />
257-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
257-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
258                <action android:name="android.intent.action.BATTERY_LOW" />
258-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
258-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
259            </intent-filter>
260        </receiver>
261        <receiver
261-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
262            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
262-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
263            android:directBootAware="false"
263-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
264            android:enabled="false"
264-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
265            android:exported="false" >
265-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
266            <intent-filter>
266-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
267                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
267-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
267-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
268                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
268-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
268-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
269            </intent-filter>
270        </receiver>
271        <receiver
271-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
272            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
272-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
273            android:directBootAware="false"
273-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
274            android:enabled="false"
274-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
275            android:exported="false" >
275-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
276            <intent-filter>
276-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
277                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
277-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
277-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
278            </intent-filter>
279        </receiver>
280        <receiver
280-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
281            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
281-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
282            android:directBootAware="false"
282-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
283            android:enabled="false"
283-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
284            android:exported="false" >
284-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
285            <intent-filter>
285-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
286                <action android:name="android.intent.action.BOOT_COMPLETED" />
286-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
286-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
287                <action android:name="android.intent.action.TIME_SET" />
287-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
287-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
288                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
288-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
288-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
289            </intent-filter>
290        </receiver>
291        <receiver
291-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
292            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
292-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
293            android:directBootAware="false"
293-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
294            android:enabled="@bool/enable_system_alarm_service_default"
294-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
295            android:exported="false" >
295-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
296            <intent-filter>
296-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
297                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
297-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
297-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
298            </intent-filter>
299        </receiver>
300        <receiver
300-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
301            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
301-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
302            android:directBootAware="false"
302-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
303            android:enabled="true"
303-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
304            android:exported="true"
304-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
305            android:permission="android.permission.DUMP" >
305-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
306            <intent-filter>
306-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
307                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
307-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
307-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ade246e5195dabf34d916bdec2a49fce\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
308            </intent-filter>
309        </receiver>
310        <receiver
310-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
311            android:name="androidx.profileinstaller.ProfileInstallReceiver"
311-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
312            android:directBootAware="false"
312-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
313            android:enabled="true"
313-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
314            android:exported="true"
314-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
315            android:permission="android.permission.DUMP" >
315-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
316            <intent-filter>
316-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
317                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
317-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
317-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
318            </intent-filter>
319            <intent-filter>
319-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
320                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
320-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
320-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
321            </intent-filter>
322            <intent-filter>
322-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
323                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
323-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
323-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
324            </intent-filter>
325            <intent-filter>
325-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
326                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
326-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
326-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d7e59e65c0683989da89981a01e18f0\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
327            </intent-filter>
328        </receiver>
329
330        <service
330-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\43e211c18de3038f20610673a4c19103\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
331            android:name="androidx.room.MultiInstanceInvalidationService"
331-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\43e211c18de3038f20610673a4c19103\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
332            android:directBootAware="true"
332-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\43e211c18de3038f20610673a4c19103\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
333            android:exported="false" />
333-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\43e211c18de3038f20610673a4c19103\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
334    </application>
335
336</manifest>
