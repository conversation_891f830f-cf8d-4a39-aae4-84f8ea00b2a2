package com.bezruk.qrcodebarcode.utility;

import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;
import android.widget.Toast;

import androidx.core.content.FileProvider;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * Helper class for file operations with modern Android storage handling
 */
public class FileHelper {

    private static final String IMAGE_DIRECTORY = "QRScannerMAX";
    private static final String IMAGE_PREFIX = "QR_";
    private static final String IMAGE_EXTENSION = ".png";

    /**
     * Save bitmap to gallery using modern storage approach
     */
    public static boolean saveBitmapToGallery(Context context, Bitmap bitmap, String fileName) {
        if (bitmap == null) return false;

        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                return saveBitmapToGalleryQ(context, bitmap, fileName);
            } else {
                return saveBitmapToGalleryLegacy(context, bitmap, fileName);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Save bitmap for Android Q+ using MediaStore
     */
    private static boolean saveBitmapToGalleryQ(Context context, Bitmap bitmap, String fileName) {
        ContentResolver resolver = context.getContentResolver();
        ContentValues contentValues = new ContentValues();
        
        contentValues.put(MediaStore.MediaColumns.DISPLAY_NAME, fileName);
        contentValues.put(MediaStore.MediaColumns.MIME_TYPE, "image/png");
        contentValues.put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_PICTURES + "/" + IMAGE_DIRECTORY);

        Uri imageUri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues);
        
        if (imageUri != null) {
            try (OutputStream outputStream = resolver.openOutputStream(imageUri)) {
                if (outputStream != null) {
                    bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream);
                    Toast.makeText(context, "Image saved to gallery", Toast.LENGTH_SHORT).show();
                    return true;
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return false;
    }

    /**
     * Save bitmap for Android versions below Q
     */
    private static boolean saveBitmapToGalleryLegacy(Context context, Bitmap bitmap, String fileName) {
        File picturesDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES);
        File appDir = new File(picturesDir, IMAGE_DIRECTORY);
        
        if (!appDir.exists()) {
            appDir.mkdirs();
        }
        
        File imageFile = new File(appDir, fileName);
        
        try (FileOutputStream outputStream = new FileOutputStream(imageFile)) {
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream);
            
            // Notify gallery about new image
            Intent mediaScanIntent = new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE);
            Uri contentUri = Uri.fromFile(imageFile);
            mediaScanIntent.setData(contentUri);
            context.sendBroadcast(mediaScanIntent);
            
            Toast.makeText(context, "Image saved to gallery", Toast.LENGTH_SHORT).show();
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Share bitmap using FileProvider
     */
    public static void shareBitmap(Context context, Bitmap bitmap, String fileName) {
        if (bitmap == null) return;

        try {
            File cachePath = new File(context.getCacheDir(), "images");
            cachePath.mkdirs();
            
            File imageFile = new File(cachePath, fileName);
            
            try (FileOutputStream outputStream = new FileOutputStream(imageFile)) {
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream);
            }
            
            Uri contentUri = FileProvider.getUriForFile(context, 
                context.getPackageName() + ".fileprovider", imageFile);
            
            Intent shareIntent = new Intent(Intent.ACTION_SEND);
            shareIntent.setType("image/png");
            shareIntent.putExtra(Intent.EXTRA_STREAM, contentUri);
            shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            
            context.startActivity(Intent.createChooser(shareIntent, "Share QR Code"));
            
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(context, "Failed to share image", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * Generate unique filename with timestamp
     */
    public static String generateFileName() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault());
        String timestamp = dateFormat.format(new Date());
        return IMAGE_PREFIX + timestamp + IMAGE_EXTENSION;
    }

    /**
     * Generate filename with custom prefix
     */
    public static String generateFileName(String prefix) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault());
        String timestamp = dateFormat.format(new Date());
        return prefix + "_" + timestamp + IMAGE_EXTENSION;
    }

    /**
     * Get cache directory for temporary files
     */
    public static File getCacheDirectory(Context context) {
        File cacheDir = new File(context.getCacheDir(), "images");
        if (!cacheDir.exists()) {
            cacheDir.mkdirs();
        }
        return cacheDir;
    }

    /**
     * Clear cache directory
     */
    public static void clearCache(Context context) {
        File cacheDir = getCacheDirectory(context);
        if (cacheDir.exists()) {
            File[] files = cacheDir.listFiles();
            if (files != null) {
                for (File file : files) {
                    file.delete();
                }
            }
        }
    }

    /**
     * Get file size in human readable format
     */
    public static String getFileSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        int exp = (int) (Math.log(bytes) / Math.log(1024));
        String pre = "KMGTPE".charAt(exp - 1) + "";
        return String.format(Locale.getDefault(), "%.1f %sB", bytes / Math.pow(1024, exp), pre);
    }
}
